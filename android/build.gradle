//buildscript {
//    ext.kotlin_version = '1.7.10'
//    repositories {
//        google()
//        mavenCentral()
//    }
//
//    dependencies {
//        classpath 'com.android.tools.build:gradle:7.3.1'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//    }
//}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://storage.googleapis.com/download.flutter.io" }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

