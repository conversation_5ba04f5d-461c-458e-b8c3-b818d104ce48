
#!/bin/bash
echo "Android 打包后的路径"
# 打包后的路径
filePath=../../build/app/outputs/apk/release/唯寻网校.apk
#获取当前git用户名
name=$(git config user.name)
#获取当前git 分支
branch=$(git rev-parse --abbrev-ref HEAD)
# 获取git 提交记录
MESSAGE=$(git log -1 HEAD --pretty=format:%s)
echo "$MESSAGE"

## 上传至OSS
echo "上传至OSS"

#function upload_to_pgyer() {
#res=$(curl -F "file=@$filePath" \
#-F "uKey=8eb23e2b81d76dbeebe3a73c53bfdc3d" \
#-F "_api_key=11cad500a370a52876b9be4df16de982" \
#-F "updateDescription=$MESSAGE" \
#https://www.pgyer.com/apiv1/app/upload)
#
#echo "[LOG] res $res"
#key=$(echo "$res" | jq -r '.data.appKey')
#url="https://www.pgyer.com/$key"
#echo "[LOG] url $url"
#qrcode=$(echo "$res" | jq -r '.data.appQRCodeURL')
#echo "[LOG] qrcode $qrcode"
#}

# 上传fir
function upload_to_fir() {

res=$(curl -X "POST" "http://api.bq04.com/apps" \
     -H "Content-Type: application/json" \
     -d "{\"type\":\"android\", \"bundle_id\":\"com.weixun.studente_education\", \"api_token\":\"13581c4350ed341b416a26cd998f7064\"}")
echo "[LOG] res $res"
key=$(echo "$res" | jq -r '.cert.binary.key')
token=$(echo "$res" | jq -r '.cert.binary.token')
echo "[LOG] key $key"
echo "[LOG] token $token"

res2=$(curl   -F "key=$key"              \
       -F "token=$token"             \
       -F "file=@$filePath"            \
       -F "x:name=唯寻网校"             \
       -F "x:version=2.0.2"         \
       -F "x:build=1"               \
       -F "x:changelog=$MESSAGE"       \
       https://upload.qbox.me)
echo "[LOG] res2 $res2"
}


## 上传至蒲公英
#upload_to_pgyer
upload_to_fir

#上传成功 发送钉钉通知

#token="1229cc01240f91e4f348b6f400cb3f85686b020caa4dca2cf71b975913ef27ae"
#Name="compute_04"
#
#curl 'https://oapi.dingtalk.com/robot/send?access_token='$token \
#    -H 'Content-Type: application/json' \
#    -d '{"msgtype": "markdown","markdown": {"title": "App 构建",
#                                            "text": "
#### App-唯寻网校-Android-构建成功-Test\n\n
#> **构建人:** '"${name}"'\n\n
#> **分支:** '"${branch}"'\n\n
#> **修改记录:** '"${MESSAGE}"'\n\n
#> ![]('"$qrcode"')
#"}}'
#curl 'https://oapi.dingtalk.com/robot/send?access_token=1229cc01240f91e4f348b6f400cb3f85686b020caa4dca2cf71b975913ef27ae' \
#   -H 'Content-Type: application/json' \
#   -d '
#  {"msgtype": "markdown",
#    "markdown": {"title": "Spark Monitor",
#                  "text": "
#### App-唯寻工作台-Android-构建成功\n\n
#> **构建人:** '${name}'\n\n
#> **分支:** '${branch}'\n\n
#> **修改记录:** '$MESSAGE'\n\n
#> ![]('$qrcode')
#"}}'
