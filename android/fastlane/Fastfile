require 'net/http'
require 'uri'
require 'json'
require 'aliyun/oss'

default_platform(:android)

# upload to oss
def upload_to_oss(file_path)
    client = Aliyun::OSS::Client.new(
      endpoint: "https://oss-cn-hangzhou.aliyuncs.com",
      access_key_id: "LTAI5t9Nhz48BwKGfEWLSKnA",
      access_key_secret: '******************************'
    )
    # 上传文件
    local_file = file_path
    remote_file = "/client/app/test"
    bucket = client.get_bucket(bucket_name)
    bucket.put_object(remote_file, file: local_file)
    puts "upload success"
    # 获取文件 URL
    object = bucket.get_object(remote_file)
    puts object.url
end

def get_latest_commit_message()
  commit_message = `git log -1 --pretty=%B`
  return commit_message
end

def uplaod_to_pgyer(file_path, env)
 res = pgyer(api_key: "11cad500a370a52876b9be4df16de982",apk: file_path)
 puts "res====#{res['buildQRCodeURL']}"
 send_dingtalk_msg(res['buildQRCodeURL'], env)
end


 #  send dingtalk message use http
def send_dingtalk_msg(buildQRCodeURL, env)
  commit_message = get_latest_commit_message()
  puts "send_dingtalk_msg"
 webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=1229cc01240f91e4f348b6f400cb3f85686b020caa4dca2cf71b975913ef27ae'
 # 构建请求体
  # ![](https://bigshot.oss-cn-shanghai.aliyuncs.com/icons/ic_android.png)
 payload = {
   msgtype: 'markdown',
   markdown: {
     title: '唯寻网校App-Android',
     text: <<~EOF
          ## 唯寻网校Android
          - **构建环境** #{env.upcase}
          - **构建分支** #{`git rev-parse --abbrev-ref HEAD`.strip}
          - **更新信息** #{commit_message}
          - **构建时间** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
          ![](#{buildQRCodeURL})
        EOF
   }
 }

 # 将请求体转换为 JSON
 request_body = payload.to_json
 uri = URI.parse(webhook_url)
 # 创建 HTTP 对象
 http = Net::HTTP.new(uri.host, uri.port)
 http.use_ssl = true # 启用 SSL
 # 构建请求
 request = Net::HTTP::Post.new(uri.request_uri)
 request['Content-Type'] = 'application/json'
 request.body = request_body
 # 发送请求
 response = http.request(request)
 if response.code == "200"
 else
   puts response.code
 end
end

platform :android do
  desc "发布 蒲公英"
  lane :test do
     gradle(task: 'clean')
     gradle(task:'assembleVaTest')
     uplaod_to_pgyer("../build/app/outputs/apk/vaTest/app-vaTest.apk", "test")
   end
  lane :uat do
     gradle(task:'assembleVaUat')
     uplaod_to_pgyer("../build/app/outputs/apk/vaUat/app-vaUat.apk", "uat")
  end
  lane :prod do
    #  gradle(task: 'clean')
     gradle(task:'assembleVaProd')
     uplaod_to_pgyer("../build/app/outputs/apk/vaProd/app-vaProd.apk", "prod")
  end
  lane :release do
    gradle(task:'assembleRelease')
    upload_to_oss("../build/app/outputs/apk/release/*.apk")
  end
end
