<?xml
    version="1.0"
    encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.weixun.studente_education">
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <queries>
        <package
            android:name="com.tencent.mm"/>
        <package
            android:name="cn.eeo.classin"/>
        <intent>
            <action
                android:name="com.getui.sdk.action"/>
        </intent>
    </queries>
    <!--
     io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here.
    -->
    <!-- iBeancon功能所需权限 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"/>
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"/>
    <!-- 个推3.0电子围栏功能所需权限 -->
    <uses-permission
        android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission
        android:name="android.permission.INTERNET"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <!-- 应用安装 -->
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission
        android:name="android.permission.WAKE_LOCK"/>
    <uses-permission
        android:name="android.permission.CAMERA"/>
    <uses-permission
        android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <!--android13-->
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage"/>
    <!--    友盟统计-->
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission
        android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"/>
    <!--    麦克风-->
    <uses-permission
        android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission
        android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT"/>
    <uses-permission
        android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <application
        android:name=".MainApplication"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config">
        <activity
            android:exported="true"
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.App.Transparent"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background"/>
            <!-- Theme to apply as soon as Flutter begins rendering frames -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <meta-data
                android:name="com.baidu.lbsapi.API_KEY"
                android:value="${BAIDU_MAP_KEY}"/>
            <meta-data
                android:name="flutterEmbedding"
                android:value="2"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <data
                    android:scheme="growing.3de51ea1e780e50b"/>
                <action
                    android:name="android.intent.action.VIEW"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
                <category
                    android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".WebViewVideoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"/>
        <activity
            android:name=".VideoActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale"
            android:theme="@style/WebViewTheme"
            android:windowSoftInputMode="adjustResize"/>
        <activity
            android:name=".PdfViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"/>
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Ucrop.CropTheme"/> <!--this line is updated-->
        <provider
            android:name=".GenericFileProvider"
            android:authorities="com.weixun.studente_education"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"
                tools:replace="android:resource"/>
        </provider>
        <receiver
            android:name=".receiver.InstallReceiver"
            android:exported="true">
            <intent-filter
                android:priority="20">
                <action
                    android:name="android.intent.action.DOWNLOAD_COMPLETE"/>
            </intent-filter>
        </receiver>
    </application>
</manifest>
