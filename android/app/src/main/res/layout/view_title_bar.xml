<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:elevation="4dp"
    android:background="@color/colorPrimary">

    <ImageView
        android:id="@+id/iv_title_bar_back"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:scaleType="fitCenter"
        android:src="@mipmap/ic_back2" />

    <ImageView
        android:id="@+id/iv_title_bar_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@id/iv_title_bar_back"
        android:paddingStart="10dp"
        android:paddingTop="17dp"
        android:paddingEnd="10dp"
        android:paddingBottom="17dp"
        android:src="@mipmap/ic_close"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_title_bar_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="110dp"
        android:layout_marginEnd="70dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textFontWeight="700"
        android:textColor="@color/black"
        android:textSize="17sp"
        tools:text="默认标题" />

    <TextView
        android:id="@+id/tv_title_bar_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:visibility="gone"
        tools:text="保存" />
</RelativeLayout>
