<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".MainActivity">

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="#ffffff">

        <ImageView
                android:id="@+id/iv_back"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:contentDescription="@string/svg_desc"
                android:paddingStart="10dp"
                android:src="@drawable/back_svg"
                tools:ignore="RtlSymmetry" />

        <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="#000000"
                android:textSize="20sp" />
    </RelativeLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#eee" />

    <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

</LinearLayout>