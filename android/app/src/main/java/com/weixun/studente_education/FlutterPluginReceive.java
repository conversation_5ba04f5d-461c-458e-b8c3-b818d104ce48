package com.weixun.studente_education;

import android.app.Activity;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry;

public class FlutterPluginReceive implements MethodChannel.MethodCallHandler {

    public static String CHANNEL = "com.weixun.studente_education.getClienId";
    static MethodChannel channel;
    private Activity activity;

    private FlutterPluginReceive(Activity activity) {
        this.activity = activity;
    }

    @Override
    public void onMethodCall(MethodCall methodCall, MethodChannel.Result result) {
        if (methodCall.method.equals("getClientId")) {

        } else {
            result.notImplemented();
        }
    }

    public static void registerWith(PluginRegistry.Registrar registrar) {
        channel = new MethodChannel(registrar.messenger(), CHANNEL);
        FlutterPluginReceive instance = new FlutterPluginReceive(registrar.activity());
        //setMethodCallHandler 在此通道上接收方法调用的回调
        channel.setMethodCallHandler(instance);
    }
}
