package com.weixun.studente_education.plugins;


import io.flutter.plugin.common.PluginRegistry;

public class MyWebViewFlutterPlugin {
    public static void registerWith(PluginRegistry registry) {
        final String key = MyWebViewFlutterPlugin.class.getCanonicalName();
        if (registry.hasPlugin(key)) return;
        PluginRegistry.Registrar registrar = registry.registrarFor(key);
        registrar.platformViewRegistry().registerViewFactory("plugins.vision.app/webView", new MyWebViewFactory(registrar.messenger(), registrar.activity()));
    }
}
