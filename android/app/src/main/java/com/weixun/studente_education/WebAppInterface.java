package com.weixun.studente_education;

import android.content.Context;
import android.util.Log;
import android.webkit.JavascriptInterface;
import org.json.JSONException;
import org.json.JSONObject;

public class WebAppInterface {

    private static final String TAG = "WebAppInterface";

    Context mContext;
    WebAppCallback callback;

    /** Instantiate the interface and set the context */
    WebAppInterface(Context c, WebAppCallback callback) {
        mContext = c;
        this.callback = callback;
    }

    /** Receive JSON message from the web page */
    @JavascriptInterface
    public void postMessage(String jsonMessage) {
        try {
//            {
//              "type":"jump",
//                "data":{
//                  "title":"ss",
//                "url":"https://www.baidu.com"
//                }
//            }
            JSONObject jsonObject = new JSONObject(jsonMessage);
            String type = jsonObject.getString("type");
            JSONObject data = jsonObject.getJSONObject("data");
            String title = data.getString("title");
            String url = data.getString("url");

            callback.onMessageReceived(title);


        } catch (JSONException e) {
            e.printStackTrace();
            // Handle error if needed
        }
    }

    public interface WebAppCallback {
        void onMessageReceived(String title);
    }
}
