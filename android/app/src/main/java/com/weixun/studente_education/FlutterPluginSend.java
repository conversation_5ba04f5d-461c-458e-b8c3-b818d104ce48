package com.weixun.studente_education;

import android.app.Activity;

import io.flutter.Log;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.PluginRegistry;

public class FlutterPluginSend implements EventChannel.StreamHandler {

    private static String CHANNEL = "com.weixun.studente_education.getClienId";

    static EventChannel channel;

    private static Activity activity;
    private static String sMsg;

    private FlutterPluginSend(Activity activity) {
        this.activity = activity;
    }

    public static void registerWith(PluginRegistry.Registrar registrar) {
        channel = new EventChannel(registrar.messenger(), CHANNEL);
        FlutterPluginSend instance = new FlutterPluginSend(registrar.activity());
        channel.setStreamHandler(instance);
        // basicMessageChannel = new BasicMessageChannel<String> ("foo", StringCodec.INSTANCE);
    }

    //发送消息
    public static void sendMsg(BinaryMessenger binaryMessenger, String msg) {
        sMsg = msg;
        EventChannel channel = new EventChannel(binaryMessenger, CHANNEL);
        FlutterPluginSend instance = new FlutterPluginSend(activity);
        channel.setStreamHandler(instance);
    }

    @Override
    public void onListen(Object o, EventChannel.EventSink eventSink) {
        //开始发送消息
        eventSink.success(sMsg);
    }

    @Override
    public void onCancel(Object o) {
        Log.i("FlutterPluginSend", "FlutterPluginSend:onCancel");
    }
}
