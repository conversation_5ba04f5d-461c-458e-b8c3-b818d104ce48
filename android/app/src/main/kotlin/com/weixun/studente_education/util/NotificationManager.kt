package com.weixun.studente_education.util

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.provider.Settings
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.weixun.studente_education.R


class NotificationManager private constructor() {

    companion object {
        const val NOTIFICATION_ID = 234
        const val CHANNEL_ID = "my_channel_01"

        @get:Synchronized
        @Volatile
        var instance: com.weixun.studente_education.util.NotificationManager? = null
            get() {
                if (field == null) {
                    synchronized(NotificationManager::class.java) {
                        if (field == null) {
                            field = NotificationManager()
                        }
                    }
                }
                return field
            }
            private set
    }

    fun showNotification(ctx: Context, title: String?, message: String?) {
        val intent: Intent = Intent()
        val pendingIntent: PendingIntent = PendingIntent.getBroadcast(ctx, 0, intent, 0)
        val notificationManager = ctx.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val builder = NotificationCompat.Builder(ctx, CHANNEL_ID)
                .setSmallIcon(R.drawable.push_small)
                .setContentTitle(ctx.getString(R.string.app_name))
                .setContentText(message)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE)
                .setVibrate(longArrayOf(0))

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name: CharSequence = "my_channel"
            val description = "This is my channel"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val mChannel = NotificationChannel(CHANNEL_ID, name, importance)
            mChannel.description = description
            mChannel.enableLights(true)
            mChannel.lightColor = Color.RED
            mChannel.enableVibration(false)
            mChannel.vibrationPattern = longArrayOf(0)
            mChannel.setShowBadge(false)
            notificationManager.createNotificationChannel(mChannel)
            builder.setChannelId(CHANNEL_ID);
        }
        notificationManager.notify(NOTIFICATION_ID, builder.build())
    }

    fun openNotificationSetting(activity: Context) {
        val packageName = "com.weixun.studente_education"
        val packageManager = activity.packageManager;
        val packageInfo = packageManager.getPackageInfo("com.weixun.studente_education", 0)
        val applicationInfo = packageInfo.applicationInfo
        val intent: Intent = Intent()
        try {
            intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS

            //8.0及以后版本使用这两个extra.  >=API 26
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
            intent.putExtra(Settings.EXTRA_CHANNEL_ID, applicationInfo.uid)

            //5.0-7.1 使用这两个extra.  <= API 25, >=API 21
            intent.putExtra("app_package", packageName)
            intent.putExtra("app_uid", applicationInfo.uid)

            activity.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()

            //其他低版本或者异常情况，走该节点。进入APP设置界面
            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            intent.putExtra("package", packageName)

            //val uri = Uri.fromParts("package", packageName, null)
            //intent.data = uri
            activity.startActivity(intent)
        }
    }

    fun isNotificationEnabled(context: Context): Boolean {
        return try {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            false
        }
    }

}