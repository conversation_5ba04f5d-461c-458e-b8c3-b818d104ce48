package com.weixun.studente_education.util

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Environment
import java.io.File


class ApkManager private constructor() {

    companion object {

        private const val SAVE_APP_NAME = "vision_academy.apk"

        private const val SAVE_APP_LOCATION = "/vision"

        val APP_FILE_NAME = Environment.getExternalStorageDirectory().path + SAVE_APP_LOCATION + File.separator + SAVE_APP_NAME
        const val DOWNLOAD_APK_ID_PREFS = "download_apk_id_prefs"

        @get:Synchronized
        @Volatile
        var instance: ApkManager? = null
            get() {
                if (field == null) {
                    synchronized(ApkManager::class.java) {
                        if (field == null) {
                            field = ApkManager()
                        }
                    }
                }
                return field
            }
            private set
    }

    fun downloadApk(context: Context, downloadUrl: String?, description: String?, infoName: String?) {
        if (!isDownloadManagerAvailable()) {
            return
        }

        var appUrl: String? = downloadUrl
        if (appUrl == null || appUrl.isEmpty()) {
            return
        }
        appUrl = appUrl.trim { it <= ' ' } // 去掉首尾空格

        if (!appUrl.startsWith("http")) {
            appUrl = "http://$appUrl" // 添加Http信息
        }


        val request: DownloadManager.Request
        try {
            request = DownloadManager.Request(Uri.parse(appUrl))
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return
        }

        request.setTitle(infoName)
        request.setDescription(description)

        //在通知栏显示下载进度
        //在通知栏显示下载进度
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            request.setVisibleInDownloadsUi(true)
            request.allowScanningByMediaScanner()
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        }

        //sdcard目录下的download文件夹
        //sdcard目录下的download文件夹

        val file = File(APP_FILE_NAME)

        if (file.exists()) { //如果文件存在 删除文件
            file.delete()
        }

        request.setDestinationInExternalPublicDir(SAVE_APP_LOCATION, SAVE_APP_NAME)

        val appContext = context.applicationContext
        val manager: DownloadManager = appContext.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager

        manager.enqueue(request)
    }

    // 最小版本号大于9
    private fun isDownloadManagerAvailable(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD
    }

//    fun installApk(context: Context, activity: Activity) {
//        try {
//            val i = Intent(Intent.ACTION_VIEW)
//            val filePath: String = APP_FILE_NAME
//            i.setDataAndType(Uri.parse("file://$filePath"), "application/vnd.android.package-archive")
//            i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//            activity.startActivity(i)
//        } catch (e: Exception) {
////            Log.e(TAG, "安装失败")
//            e.printStackTrace()
//        }
//    }
}