package com.weixun.studente_education.constant


class SPConstant {

    companion object {

        //打开通知
        const val METHOD_NAME_OPEN_NOTIFICATION = "openNotification"

        //检查通知开关是否打开
        const val METHOD_NAME_CHECK_NOTIFICATION_STATUS = "checkNotificationStatus"

        //下载Apk文件
        const val METHOD_NAME_DOWNLOAD_APK = "downloadApk"

        //强制横屏
        const val METHOD_NAME_ORIENTATIONLANDSRIGHT = "DeviceOrientationLandsRight"

        //强制竖屏
        const val METHOD_NAME_ORIENTATIONPORTRAITUP = "DeviceOrientationportraitUp"

        //横竖屏切换
        const val METHOD_NAME_ORIENTATIONRIGHTPORTRAITUP = "DeviceOrientationportraitUp|right"

        //打开Activity
        const val METHOD_OPEN_ACTIVITY = "openWebViewActivity"

        //检查定位开关打开的状态
        const val METHOD_LOCATION_SWITCH_STATUS = "checkLocationIsOpen"

        //打开设置
        const val METHOD_OPEN_SETTINGS = "openSetting"

        // 接收到flutter 发送的消息(个推)
        const val METHOD_RECEIVE_MESSAGE = "receiveMessage"

        // 打开pdf预览
        const val METHOD_OPEN_PDF = "openPdfViewActivity"
    }
}
