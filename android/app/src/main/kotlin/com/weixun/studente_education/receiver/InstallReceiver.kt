package com.weixun.studente_education.receiver

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import java.io.File


class InstallReceiver : BroadcastReceiver() {

    companion object {
        private const val SAVE_APP_NAME = "vision_academy.apk"

        private const val SAVE_APP_LOCATION = "/vision"

        val APP_FILE_NAME = "/sdcard" + SAVE_APP_LOCATION + File.separator + SAVE_APP_NAME
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent!!.action == (DownloadManager.ACTION_DOWNLOAD_COMPLETE)) {
//            val downloadApkId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            installApk(context!!)
        }
    }

    // 安装Apk
    private fun installApk(context: Context) {
        try {
            val i = Intent(Intent.ACTION_VIEW)
            val filePath: String = APP_FILE_NAME;
            val apkUri: Uri = FileProvider.getUriForFile(context, "com.weixun.studente_education", File(filePath))
            i.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            i.setDataAndType(apkUri, "application/vnd.android.package-archive")
            context.startActivity(i)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}