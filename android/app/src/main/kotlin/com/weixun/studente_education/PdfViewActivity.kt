package com.weixun.studente_education

import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.github.barteksc.pdfviewer.PDFView
import java.io.File

class PdfViewActivity : AppCompatActivity() {

    private var pdfView: PDFView? = null
    private var mTvTitle: TextView? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pdf_view)
        mTvTitle = findViewById<TextView>(R.id.tv_title_bar_title)
        pdfView = findViewById<PDFView>(
            R.id.pdfView
        )

        // 返回键
        val mTvBack = findViewById<ImageView>(R.id.iv_title_bar_back)

        mTvBack.setOnClickListener {
            finish()
        }

        //获取参数

        //获取参数
        val intent = intent
        val path = intent.getStringExtra("path")
        val title = intent.getStringExtra("title")
        mTvTitle?.text = title

        pdfView?.fromFile(File(path!!))?.load()
    }
}
