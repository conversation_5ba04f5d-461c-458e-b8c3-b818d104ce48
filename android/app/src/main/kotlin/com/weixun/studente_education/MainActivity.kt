package com.weixun.studente_education

import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.WindowManager
import com.umeng.analytics.MobclickAgent
import com.weixun.studente_education.constant.SPConstant
import com.weixun.studente_education.util.ApkManager
import com.weixun.studente_education.util.NotificationManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel


class MainActivity : FlutterActivity() {
    private val channelId = "com.wei_xun.student_education"
    private lateinit var channel: MethodChannel

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, channelId)
        channel.setMethodCallHandler { call, result ->
            onReceiveMessageFromFlutter(call, result)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    /**
     * flutter 传递过来的数据(method name)
     *
     */
    private fun onReceiveMessageFromFlutter(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            //打开通知开关
            SPConstant.METHOD_NAME_OPEN_NOTIFICATION -> NotificationManager.instance!!.openNotificationSetting(
                this
            )
            //检查是否开启了通知权限
            SPConstant.METHOD_NAME_CHECK_NOTIFICATION_STATUS -> {
                val isOpen = NotificationManager.instance!!.isNotificationEnabled(this)
                if (isOpen) {
                    result.success(isOpen)
                } else {
                    result.success(false)
                }
            }
            //下载Apk
            SPConstant.METHOD_NAME_DOWNLOAD_APK -> {
                val downloadUrl = call.argument<String>("downloadUrl")
                val description = call.argument<String>("description")
                val infoName = call.argument<String>("infoName")
                ApkManager.instance!!.downloadApk(
                    applicationContext,
                    downloadUrl,
                    description,
                    infoName
                )
            }

            ////强制横屏
            SPConstant.METHOD_NAME_ORIENTATIONLANDSRIGHT -> {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE)
            }

            //强制竖屏
            SPConstant.METHOD_NAME_ORIENTATIONPORTRAITUP -> {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)

            }

            //横竖屏切换
            SPConstant.METHOD_NAME_ORIENTATIONRIGHTPORTRAITUP -> {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED)
            }

            //打开Activity
            SPConstant.METHOD_OPEN_ACTIVITY -> {
                val url = call.argument<String>("url")
                val title = call.argument<String>("title")
                val token = call.argument<String>("token")
                val intent = Intent(this, VideoActivity::class.java)
                intent.putExtra("url", url)
                intent.putExtra("title", title)
                intent.putExtra("token", token)
                startActivity(intent)
            }

            // 检查定位开关是否打开
            SPConstant.METHOD_LOCATION_SWITCH_STATUS -> {
                checkLocationIsOpen(result);
            }

            //打开设置
            SPConstant.METHOD_OPEN_SETTINGS -> {
                openSetting()
            }

            //推送
            SPConstant.METHOD_RECEIVE_MESSAGE -> {
                val title = call.argument<String>("title")
                val content = call.argument<String>("content")
                NotificationManager.instance!!.showNotification(context, title, content)
            }

            // pdf预览
            SPConstant.METHOD_OPEN_PDF -> {
                val path = call.argument<String>("path")
                val title = call.argument<String>("title")
                val intent = Intent(this, PdfViewActivity::class.java)
                intent.putExtra("path", path)
                intent.putExtra("title", title)
                startActivity(intent)
            }

            else -> {
            }

        }
    }

    private fun checkLocationIsOpen(result: MethodChannel.Result) {
        result.success(isLocationEnabled())
    }

    private fun isLocationEnabled(): Boolean {
        val locationMode: Int
        val locationProviders: String
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            locationMode = try {
                Settings.Secure.getInt(contentResolver, Settings.Secure.LOCATION_MODE)
            } catch (e: Settings.SettingNotFoundException) {
                e.printStackTrace()
                return false
            }
            locationMode != Settings.Secure.LOCATION_MODE_OFF
        } else {
            locationProviders = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.LOCATION_PROVIDERS_ALLOWED
            )
            !TextUtils.isEmpty(locationProviders)
        }
    }

    /**
     * 打开定位服务设置页
     */
    private fun openSetting() {
        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        val packageName = "com.weixun.studente_education"
        val packageManager = packageManager;
        val packageInfo = packageManager.getPackageInfo("com.weixun.studente_education", 0)
        val applicationInfo = packageInfo.applicationInfo

        //8.0及以后版本使用这两个extra.  >=API 26
        intent.putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
        intent.putExtra(Settings.EXTRA_CHANNEL_ID, applicationInfo.uid)

        //5.0-7.1 使用这两个extra.  <= API 25, >=API 21
        intent.putExtra("app_package", packageName)
        intent.putExtra("app_uid", applicationInfo.uid)
        startActivity(intent)
    }

    override fun onConfigurationChanged(config: Configuration) {
        super.onConfigurationChanged(config)
        Log.d("onConfigurationChanged", "onConfigurationChanged------")
        when (config.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                window.clearFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN)
                window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            }

            Configuration.ORIENTATION_PORTRAIT -> {
                window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                window.addFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN)
            }
//            Configuration.ORIENTATION_ -> {
//                window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
//                window.addFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN)
//            }
        }
    }

    override fun onResume() {
        super.onResume()
        MobclickAgent.onResume(this);
    }

    override fun onPause() {
        super.onPause()
        MobclickAgent.onPause(this)
    }
}
