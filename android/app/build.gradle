plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "kotlin-android-extensions"
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader -> localProperties.load(reader)
    }
}

def env = 'Prod'
android {
    compileSdk 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        applicationId 'com.weixun.studente_education'
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 82
        versionName '2.3.20'
        multiDexEnabled true
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        manifestPlaceholders = [GETUI_APPID     : getAppId(env),
                                GETUI_APP_KEY   : getAppKey(env),
                                GETUI_APP_SECRET: getAppSecret(env),
                                BAIDU_MAP_KEY   : '6RGDgVxPWN68DZLtjhtPYDcZ0k4s38EC'  /// 百度地图key
        ]
        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    dexOptions {
        javaMaxHeapSize '4g'
    }

    signingConfigs {
        releaseConfig {
            storeFile file('./vision.keystore')
            storePassword 'vision1234'
            keyAlias 'vision'
            keyPassword 'vision1234'
        }
        debugConfig {
            storeFile file('./vision.keystore')
            storePassword 'vision1234'
            keyAlias 'vision'
            keyPassword 'vision1234'
        }
    }

    buildTypes {
        release {
            manifestPlaceholders = [GETUI_APPID     : "rAx7KdwIwG8q9HRrj5jGK9",
                                    GETUI_APP_KEY   : "hHlHPfUvrl7IvHjBMVQBy1",
                                    GETUI_APP_SECRET: "N7m543Tj8h6ZNqu8AcgeT6",
                                    BAIDU_MAP_KEY   : '6RGDgVxPWN68DZLtjhtPYDcZ0k4s38EC']
            shrinkResources false
            minifyEnabled false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseConfig
            buildConfigField "String", "UMENG_KEY", "\"5e81a303167eddc3f700004a\""
        }
        debug {
            minifyEnabled false
            zipAlignEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debugConfig
            buildConfigField "String", "UMENG_KEY", "\"5e78645a570df35e3700006d\""
        }
        vaTest {
            manifestPlaceholders = [GETUI_APPID     : "hxSISMD55h8blEcbNnik15",
                                    GETUI_APP_KEY   : "5VShdqwerJ6NB5mFt65yY2",
                                    GETUI_APP_SECRET: "TN3xalGFqn9QML0GxD20n4",
                                    BAIDU_MAP_KEY   : '6RGDgVxPWN68DZLtjhtPYDcZ0k4s38EC']
            shrinkResources false
            minifyEnabled false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseConfig
            buildConfigField "String", "UMENG_KEY", "\"5e78645a570df35e3700006d\""
        }
        vaUat {
            manifestPlaceholders = [GETUI_APPID     : "j7pxgKjPC67ziWSsWM1L72",
                                    GETUI_APP_KEY   : "ijUtuHjcwq7wqdo9CwD0V2",
                                    GETUI_APP_SECRET: "jeX3lMROPKAtnytgaD0I02",
                                    BAIDU_MAP_KEY   : '6RGDgVxPWN68DZLtjhtPYDcZ0k4s38EC']
            shrinkResources false
            minifyEnabled false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseConfig
            buildConfigField "String", "UMENG_KEY", "\"5e78645a570df35e3700006d\""
        }
        vaProd {
            manifestPlaceholders = [GETUI_APPID     : "rAx7KdwIwG8q9HRrj5jGK9",
                                    GETUI_APP_KEY   : "hHlHPfUvrl7IvHjBMVQBy1",
                                    GETUI_APP_SECRET: "N7m543Tj8h6ZNqu8AcgeT6",
                                    BAIDU_MAP_KEY   : '6RGDgVxPWN68DZLtjhtPYDcZ0k4s38EC'

            ]
            shrinkResources false
            minifyEnabled false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseConfig
            buildConfigField "String", "UMENG_KEY", "\"5e81a303167eddc3f700004a\""
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        // exclude ARMEABI native so file, ARMEABI has been removed in NDK r17.
        exclude 'lib/armeabi/**'
    }

    flavorDimensions 'default'

    // buildToolsVersion = '28.0.3'

//     applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.all {
            if (variant.buildType.name == 'release') {
                outputFileName = '唯寻网校.apk'
            }
        }
    }
}

/**
 * 获取个推AppSecret
 * @param env
 * @return
 */
private static String getAppSecret(String env) {
    switch (env) {
        case 'Test':
        case 'Dev':
            return 'TN3xalGFqn9QML0GxD20n4'
        case 'Uat':
            return 'jeX3lMROPKAtnytgaD0I02'
        case 'Prod':
            return 'N7m543Tj8h6ZNqu8AcgeT6'
        default:
            return 'N7m543Tj8h6ZNqu8AcgeT6'
    }
}

/**
 * 获取个推AppKey
 * @param env
 * @return
 */
private static String getAppKey(String env) {
    switch (env) {
        case 'Test':
        case 'Dev':
            return '5VShdqwerJ6NB5mFt65yY2'
        case 'Uat':
            return 'ijUtuHjcwq7wqdo9CwD0V2'
        case 'Prod':
            return 'hHlHPfUvrl7IvHjBMVQBy1'
        default:
            return 'hHlHPfUvrl7IvHjBMVQBy1'
    }
}

/**
 * 获取个推AppId
 * @param env
 * @return
 */
private static String getAppId(String env) {
    switch (env) {
        case 'Test':
        case 'Dev':
            return 'hxSISMD55h8blEcbNnik15'
        case 'Uat':
            return 'j7pxgKjPC67ziWSsWM1L72'
        case 'Prod':
            return 'rAx7KdwIwG8q9HRrj5jGK9'
        default:
            return 'rAx7KdwIwG8q9HRrj5jGK9'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    implementation 'androidx.core:core:1.1.0'
//    com.github.mhiew:android-pdf-viewer:3.2.0-beta.1
    implementation 'com.github.mhiew:android-pdf-viewer:3.2.0-beta.1'
//    implementation 'com.github.barteksc:android-pdf-viewer:3.0.0-beta.5'
//    implementation 'com.github.barteksc:android-pdf-viewer:2.8.2'
}
