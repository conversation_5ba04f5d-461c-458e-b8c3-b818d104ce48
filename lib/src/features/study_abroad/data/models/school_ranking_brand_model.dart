import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/school_ranking_brand.dart';

part 'school_ranking_brand_model.g.dart';

@JsonSerializable()
class SchoolRankingBrandModel {
  @JsonKey(name: 'rankBrandUid')
  final String rankBrandUid;

  @Json<PERSON>ey(name: 'rankBrandName')
  final String rankBrandName;

  @Json<PERSON>ey(name: 'rankBrandUrl')
  final String rankBrandUrl;

  @JsonKey(name: 'ossRankBrandUrl')
  final String ossRankBrandUrl;

  const SchoolRankingBrandModel({
    required this.rankBrandUid,
    required this.rankBrandName,
    required this.rankBrandUrl,
    required this.ossRankBrandUrl,
  });

  factory SchoolRankingBrandModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandModelFromJson(json);

  Map<String, dynamic> toJson() => _$SchoolRankingBrandModelToJson(this);

  SchoolRankingBrand toEntity() {
    return SchoolRankingBrand(
      rankBrandUid: rankBrandUid,
      rankBrandName: rankBrandName,
      rankBrandUrl: rankBrandUrl,
      ossRankBrandUrl: ossRankBrandUrl,
    );
  }
}

@JsonSerializable()
class SchoolRankingBrandResponseModel {
  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String message;

  @JsonKey(name: 'data')
  final SchoolRankingBrandDataModel? data;

  const SchoolRankingBrandResponseModel({
    required this.success,
    required this.message,
    this.data,
  });

  factory SchoolRankingBrandResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$SchoolRankingBrandResponseModelToJson(this);
}

@JsonSerializable()
class SchoolRankingBrandDataModel {
  @JsonKey(name: 'dataList')
  final List<SchoolRankingBrandModel> dataList;

  const SchoolRankingBrandDataModel({
    required this.dataList,
  });

  factory SchoolRankingBrandDataModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$SchoolRankingBrandDataModelToJson(this);
}
