import 'package:json_annotation/json_annotation.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/entities/school_ranking_brand.dart';

part 'school_ranking_brand_model.g.dart';

@JsonSerializable()
class SchoolRankingBrandResponseModel {
  final String code;
  final String message;
  final SchoolRankingBrandDataModel? data;
  final bool success;

  SchoolRankingBrandResponseModel({
    required this.code,
    required this.message,
    this.data,
    required this.success,
  });

  factory SchoolRankingBrandResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandResponseModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$SchoolRankingBrandResponseModelToJson(this);
}

@JsonSerializable()
class SchoolRankingBrandDataModel {
  final List<SchoolRankingBrandItemModel> dataList;

  SchoolRankingBrandDataModel({
    required this.dataList,
  });

  factory SchoolRankingBrandDataModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$SchoolRankingBrandDataModelToJson(this);
}

@JsonSerializable()
class SchoolRankingBrandItemModel {
  final String rankBrandUid;
  final String rankBrandName;
  final String rankBrandUrl;
  final String ossRankBrandUrl;

  SchoolRankingBrandItemModel({
    required this.rankBrandUid,
    required this.rankBrandName,
    required this.rankBrandUrl,
    required this.ossRankBrandUrl,
  });

  factory SchoolRankingBrandItemModel.fromJson(Map<String, dynamic> json) =>
      _$SchoolRankingBrandItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$SchoolRankingBrandItemModelToJson(this);

  SchoolRankingBrand toEntity() {
    return SchoolRankingBrand(
      rankBrandUid: rankBrandUid,
      rankBrandName: rankBrandName,
      rankBrandUrl: rankBrandUrl,
      ossRankBrandUrl: ossRankBrandUrl,
    );
  }
}
