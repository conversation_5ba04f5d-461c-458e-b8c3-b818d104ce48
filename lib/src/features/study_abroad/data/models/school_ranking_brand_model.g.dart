// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'school_ranking_brand_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SchoolRankingBrandResponseModel _$SchoolRankingBrandResponseModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandResponseModel(
      code: json['code'] as String,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : SchoolRankingBrandDataModel.fromJson(
              json['data'] as Map<String, dynamic>),
      success: json['success'] as bool,
    );

Map<String, dynamic> _$SchoolRankingBrandResponseModelToJson(
        SchoolRankingBrandResponseModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'data': instance.data,
      'success': instance.success,
    };

SchoolRankingBrandDataModel _$SchoolRankingBrandDataModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandDataModel(
      dataList: (json['dataList'] as List<dynamic>)
          .map((e) =>
              SchoolRankingBrandItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SchoolRankingBrandDataModelToJson(
        SchoolRankingBrandDataModel instance) =>
    <String, dynamic>{
      'dataList': instance.dataList,
    };

SchoolRankingBrandItemModel _$SchoolRankingBrandItemModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandItemModel(
      rankBrandUid: json['rankBrandUid'] as String,
      rankBrandName: json['rankBrandName'] as String,
      rankBrandUrl: json['rankBrandUrl'] as String,
      ossRankBrandUrl: json['ossRankBrandUrl'] as String,
    );

Map<String, dynamic> _$SchoolRankingBrandItemModelToJson(
        SchoolRankingBrandItemModel instance) =>
    <String, dynamic>{
      'rankBrandUid': instance.rankBrandUid,
      'rankBrandName': instance.rankBrandName,
      'rankBrandUrl': instance.rankBrandUrl,
      'ossRankBrandUrl': instance.ossRankBrandUrl,
    };
