// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'school_ranking_brand_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SchoolRankingBrandModel _$SchoolRankingBrandModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandModel(
      rankBrandUid: json['rankBrandUid'] as String,
      rankBrandName: json['rankBrandName'] as String,
      rankBrandUrl: json['rankBrandUrl'] as String,
      ossRankBrandUrl: json['ossRankBrandUrl'] as String,
    );

Map<String, dynamic> _$SchoolRankingBrandModelToJson(
        SchoolRankingBrandModel instance) =>
    <String, dynamic>{
      'rankBrandUid': instance.rankBrandUid,
      'rankBrandName': instance.rankBrandName,
      'rankBrandUrl': instance.rankBrandUrl,
      'ossRankBrandUrl': instance.ossRankBrandUrl,
    };

SchoolRankingBrandResponseModel _$SchoolRankingBrandResponseModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : SchoolRankingBrandDataModel.fromJson(
              json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SchoolRankingBrandResponseModelToJson(
        SchoolRankingBrandResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

SchoolRankingBrandDataModel _$SchoolRankingBrandDataModelFromJson(
        Map<String, dynamic> json) =>
    SchoolRankingBrandDataModel(
      dataList: (json['dataList'] as List<dynamic>)
          .map((e) =>
              SchoolRankingBrandModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SchoolRankingBrandDataModelToJson(
        SchoolRankingBrandDataModel instance) =>
    <String, dynamic>{
      'dataList': instance.dataList,
    };
