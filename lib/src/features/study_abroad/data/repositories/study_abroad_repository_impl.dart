import 'package:dartz/dartz.dart';
import 'package:va_student_phone/src/core/errors/failures.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/entities/study_abroad_hotspot.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/entities/wx_liver.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/entities/school_ranking_brand.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/repositories/study_abroad_repository.dart';
import '../datasources/study_abroad_remote_data_source.dart';

class StudyAbroadRepositoryImpl implements StudyAbroadRepository {
  final StudyAbroadRemoteDataSource remoteDataSource;

  StudyAbroadRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<StudyAbroadHotspot>>> getStudyAbroadHotspots({
    required int pageGroupId,
    int pageNumber = 1,
    int pageSize = 10,
  }) async {
    try {
      final response = await remoteDataSource.getStudyAbroadHotspots(
        pageGroupId: pageGroupId,
        pageNumber: pageNumber,
        pageSize: pageSize,
      );

      if (response.success && response.data != null) {
        final hotspots =
            response.data!.data.map((model) => model.toEntity()).toList();
        return Right(hotspots);
      } else {
        return Left(ServerFailure(response.message));
      }
    } catch (e) {
      return Left(ServerFailure('Failed to load study abroad hotspots: $e'));
    }
  }

  @override
  Future<Either<Failure, List<WxLiver>>> getWxLiverList({
    String? appId,
    int pageIndex = 0,
    int pageSize = 10,
  }) async {
    try {
      final response = await remoteDataSource.getWxLiverList(
        appId: appId,
        pageIndex: pageIndex,
        pageSize: pageSize,
      );

      if (response.success && response.data != null) {
        final wxLiverList =
            response.data!.data.map((model) => model.toEntity()).toList();
        return Right(wxLiverList);
      } else {
        return Left(ServerFailure(response.message));
      }
    } catch (e) {
      return Left(ServerFailure('Failed to load wx liver list: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SchoolRankingBrand>>>
      getSchoolRankingBrands() async {
    try {
      final response = await remoteDataSource.getSchoolRankingBrands();

      if (response.success && response.data != null) {
        // 过滤出QS、USNEWS、TIMES的品牌信息
        final targetBrands = ['QS', 'USNEWS', 'TIMES'];
        final filteredBrands = response.data!.dataList
            .where((brand) => targetBrands.contains(brand.rankBrandName))
            .map((brand) => brand.toEntity())
            .toList();

        return Right(filteredBrands);
      } else {
        return Left(ServerFailure(response.message));
      }
    } catch (e) {
      return Left(ServerFailure('Failed to load school ranking brands: $e'));
    }
  }
}
