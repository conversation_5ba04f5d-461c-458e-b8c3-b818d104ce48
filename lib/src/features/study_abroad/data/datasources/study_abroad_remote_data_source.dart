import 'package:va_student_phone/src/core/config/api_config.dart';
import 'package:va_student_phone/src/core/network/dio_client.dart';
import '../models/study_abroad_hotspot_model.dart';
import '../models/wx_liver_model.dart';
import '../models/school_ranking_brand_model.dart';

abstract class StudyAbroadRemoteDataSource {
  Future<StudyAbroadHotspotResponseModel> getStudyAbroadHotspots({
    required int pageGroupId,
    int pageNumber = 1,
    int pageSize = 10,
  });

  Future<WxLiverResponseModel> getWxLiverList({
    String? appId,
    int pageIndex = 0,
    int pageSize = 10,
  });

  Future<SchoolRankingBrandResponseModel> getSchoolRankingBrands();
}

class StudyAbroadRemoteDataSourceImpl implements StudyAbroadRemoteDataSource {
  final DioClient dioClient;

  StudyAbroadRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<StudyAbroadHotspotResponseModel> getStudyAbroadHotspots({
    required int pageGroupId,
    int pageNumber = 1,
    int pageSize = 10,
  }) async {
    try {
      const String path = '/api/vscms/page/listByPageGroup';
      const String fullUrl = ApiEnvironment.adBaseUrl + path;
      final response = await dioClient.post(
        fullUrl,
        data: {
          "pageGroupId": pageGroupId,
          "pageNumber": pageNumber,
          "pageSize": pageSize,
          "pageStart": (pageNumber - 1) * pageSize,
        },
      );
      return StudyAbroadHotspotResponseModel.fromJson(response.data);
    } catch (e, s) {
      print(s);
      throw Exception('Failed to load study abroad hotspots: $e');
    }
  }

  @override
  Future<WxLiverResponseModel> getWxLiverList({
    String? appId,
    int pageIndex = 0,
    int pageSize = 3,
  }) async {
    try {
      const String path = '/api/scrm-mp/wx/listWxLiver';
      final String fullUrl = ApiEnvironment.adBaseUrl + path;

      final response = await dioClient.post(
        fullUrl,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        },
      );

      return WxLiverResponseModel.fromJson(response.data);
    } catch (e, s) {
      print('本科留学圈接口调用失败: $e');
      throw Exception('Failed to load wx liver list: $e');
    }
  }

  @override
  Future<SchoolRankingBrandResponseModel> getSchoolRankingBrands() async {
    try {
      const String path =
          'management/college-library/public/v1/api/school/listRankBrandSelect';
      const String fullUrl = ApiEnvironment.uatBaseUrl + path;

      final response = await dioClient.post(fullUrl);

      return SchoolRankingBrandResponseModel.fromJson(response.data);
    } catch (e, s) {
      print('学校排名品牌接口调用失败: $e');
      throw Exception('Failed to load school ranking brands: $e');
    }
  }
}
