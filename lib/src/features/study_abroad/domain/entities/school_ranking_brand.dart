import 'package:equatable/equatable.dart';

class SchoolRankingBrand extends Equatable {
  final String rankBrandUid;
  final String rankBrandName;
  final String rankBrandUrl;
  final String ossRankBrandUrl;

  const SchoolRankingBrand({
    required this.rankBrandUid,
    required this.rankBrandName,
    required this.rankBrandUrl,
    required this.ossRankBrandUrl,
  });

  @override
  List<Object?> get props => [
        rankBrandUid,
        rankBrandName,
        rankBrandUrl,
        ossRankBrandUrl,
      ];
}
