import 'package:dartz/dartz.dart';
import 'package:va_student_phone/src/core/errors/failures.dart';
import 'package:va_student_phone/src/core/usecases/usecase.dart';
import '../entities/school_ranking_brand.dart';
import '../repositories/study_abroad_repository.dart';

class GetSchoolRankingBrandUidsUseCase
    implements UseCase<List<SchoolRankingBrand>, NoParams> {
  final StudyAbroadRepository repository;

  GetSchoolRankingBrandUidsUseCase(this.repository);

  @override
  Future<Either<Failure, List<SchoolRankingBrand>>> call(
      NoParams params) async {
    return await repository.getSchoolRankingBrands();
  }
}
