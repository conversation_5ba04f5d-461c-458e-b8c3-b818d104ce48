import 'package:dartz/dartz.dart';
import 'package:va_student_phone/src/core/errors/failures.dart';
import '../entities/school_ranking_brand.dart';
import '../repositories/study_abroad_repository.dart';

class GetSchoolRankingBrandUidsUseCase {
  final StudyAbroadRepository repository;

  GetSchoolRankingBrandUidsUseCase(this.repository);

  Future<Either<Failure, List<SchoolRankingBrand>>> call() async {
    return await repository.getSchoolRankingBrands();
  }
}
