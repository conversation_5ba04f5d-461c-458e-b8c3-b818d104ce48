import 'package:dartz/dartz.dart';
import 'package:va_student_phone/src/core/errors/failures.dart';
import '../entities/study_abroad_hotspot.dart';
import '../entities/wx_liver.dart';
import '../entities/school_ranking_brand.dart';

abstract class StudyAbroadRepository {
  Future<Either<Failure, List<StudyAbroadHotspot>>> getStudyAbroadHotspots({
    required int pageGroupId,
    int pageNumber = 1,
    int pageSize = 10,
  });

  Future<Either<Failure, List<WxLiver>>> getWxLiverList({
    String? appId,
    int pageIndex = 0,
    int pageSize = 10,
  });

  Future<Either<Failure, List<SchoolRankingBrand>>> getSchoolRankingBrands();
}
