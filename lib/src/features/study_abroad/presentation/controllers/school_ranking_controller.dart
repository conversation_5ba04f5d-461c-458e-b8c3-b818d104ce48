import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../domain/entities/school_ranking_brand.dart';
import '../../domain/usecases/get_school_ranking_brand_uids_usecase.dart';

class SchoolRankingController extends GetxController {
  final GetSchoolRankingBrandUidsUseCase getSchoolRankingBrandUidsUseCase;

  SchoolRankingController({
    required this.getSchoolRankingBrandUidsUseCase,
  });

  // 响应式状态
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<SchoolRankingBrand> schoolRankingBrands = <SchoolRankingBrand>[].obs;

  // 过滤后的品牌 UIDs (QS, USNEWS, TIMES)
  final RxList<String> filteredBrandUids = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadSchoolRankingBrands();
  }

  /// 加载学校排名品牌数据
  Future<void> loadSchoolRankingBrands() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await getSchoolRankingBrandUidsUseCase.call();

      result.fold(
        (failure) {
          errorMessage.value = failure.message;
          debugPrint('加载学校排名品牌失败: ${failure.message}');
        },
        (brands) {
          schoolRankingBrands.value = brands;
          
          // 提取 QS, USNEWS, TIMES 的 UIDs
          filteredBrandUids.value = brands
              .where((brand) => ['QS', 'USNEWS', 'TIMES'].contains(brand.rankBrandName))
              .map((brand) => brand.rankBrandUid)
              .toList();

          debugPrint('成功加载学校排名品牌: ${brands.length} 个');
          debugPrint('过滤后的品牌UIDs: $filteredBrandUids');
        },
      );
    } catch (e) {
      errorMessage.value = '加载学校排名品牌时发生错误: $e';
      debugPrint('加载学校排名品牌异常: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 根据品牌名称获取对应的 UID
  String? getUidByBrandName(String brandName) {
    try {
      final brand = schoolRankingBrands.firstWhere(
        (brand) => brand.rankBrandName == brandName,
      );
      return brand.rankBrandUid;
    } catch (e) {
      debugPrint('未找到品牌: $brandName');
      return null;
    }
  }

  /// 获取所有过滤后的品牌 UIDs
  List<String> get allFilteredUids => filteredBrandUids.toList();

  /// 检查是否有数据
  bool get hasData => schoolRankingBrands.isNotEmpty;

  /// 检查是否有错误
  bool get hasError => errorMessage.value.isNotEmpty;
}
