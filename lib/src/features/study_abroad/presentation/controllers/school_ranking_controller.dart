import 'package:get/get.dart';
import 'package:va_student_phone/src/core/usecases/usecase.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/entities/school_ranking_brand.dart';
import 'package:va_student_phone/src/features/study_abroad/domain/usecases/get_school_ranking_brand_uids_usecase.dart';

class SchoolRankingController extends GetxController {
  final GetSchoolRankingBrandUidsUseCase getSchoolRankingBrandUidsUseCase;

  SchoolRankingController({
    required this.getSchoolRankingBrandUidsUseCase,
  });

  // 响应式变量
  final RxList<SchoolRankingBrand> schoolRankingBrands =
      <SchoolRankingBrand>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadSchoolRankingBrandUids();
  }

  // 加载学校排名品牌UIDs
  Future<void> loadSchoolRankingBrandUids() async {
    isLoading.value = true;
    errorMessage.value = '';

    final result = await getSchoolRankingBrandUidsUseCase(NoParams());
    result.fold(
      (failure) {
        errorMessage.value = failure.message;
        isLoading.value = false;
      },
      (brands) {
        schoolRankingBrands.value = brands;
        isLoading.value = false;
        print('获取到的学校排名品牌: $brands');
      },
    );
  }

  // 根据品牌名称获取对应的UID
  String? getUidByBrandName(String brandName) {
    final brand = schoolRankingBrands.firstWhereOrNull(
      (brand) => brand.rankBrandName == brandName,
    );
    return brand?.rankBrandUid;
  }

  // 处理学校排名点击事件
  void handleSchoolRankOnTap(String title) {
    final uid = getUidByBrandName(title);
    print('学校排名点击: $title, UID: $uid');
    // 这里可以添加具体的跳转逻辑
  }
}
