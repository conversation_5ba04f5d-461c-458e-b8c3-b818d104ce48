import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:va_student_phone/resource/image_resource.dart';
import 'package:va_student_phone/src/core/mixins/auth_check_mixin.dart';
import 'package:va_student_phone/src/styles/va_colors.dart';
import '../controllers/school_ranking_controller.dart';

class SchoolRankingWidget extends StatefulWidget with AuthCheckMixin {
  const SchoolRankingWidget({
    Key? key,
  }) : super(key: key);

  @override
  State<SchoolRankingWidget> createState() => _SchoolRankingWidgetState();
}

class _SchoolRankingWidgetState extends State<SchoolRankingWidget> {
  late final SchoolRankingController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<SchoolRankingController>();
  }

  // item点击事件
  void _handleSchoolRankOnTap(String title) {
    debugPrint('学校排名======');
    controller.handleSchoolRankOnTap(title);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: Colors.white,
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 20),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 15),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "历年各大权威榜单",
                      style: TextStyle(
                          color: VAColors.lightGrayColor, fontSize: 12.0),
                    ),
                    SizedBox(width: 5),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                      decoration: BoxDecoration(
                        color: Color(0xFFE0ECFC),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      child: Text(
                        "2025最新已出",
                        style: TextStyle(
                            color: Color(0xFF58759B), fontSize: 12.0),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 112,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildSchoolRankingListItem(
                        title: "QS",
                        subtitle: "美国大学排行",
                        imageUrl: ImageResource.school_ranking_qs),
                    _buildSchoolRankingListItem(
                        title: "USNEWS",
                        subtitle: "美国大学排行",
                        imageUrl: ImageResource.school_ranking_usNews),
                    _buildSchoolRankingListItem(
                        title: "TIMES",
                        subtitle: "美国大学排行",
                        imageUrl: ImageResource.school_ranking_times),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
      ],
    );
  }

  Widget _buildSchoolRankingListItem({
    required String title,
    required String subtitle,
    required String imageUrl,
  }) {
    return GestureDetector(
      onTap: () => _handleSchoolRankOnTap(title),
      child: Container(
        width: 112,
        height: 112,
        margin: const EdgeInsets.only(right: 10.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(color: Colors.grey.withOpacity(0.2), blurRadius: 5)
          ],
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.asset(imageUrl, width: 112, height: 112, fit: BoxFit.cover)
            ),
            Padding(
              padding:
                  const EdgeInsets.only(left: 10.0, right: 10.0, bottom: 10.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Expanded(child: SizedBox()),
                  Text(
                    title,
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18.0,
                        fontWeight: FontWeight.w700,
                        height: 1),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10.0,
                        fontWeight: FontWeight.w400,
                        height: 1),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
