import 'package:get/get.dart';
import 'package:va_student_phone/src/core/network/dio_client.dart';
import '../../data/datasources/study_abroad_remote_data_source.dart';
import '../../data/repositories/study_abroad_repository_impl.dart';
import '../../domain/repositories/study_abroad_repository.dart';
import '../../domain/usecases/get_study_abroad_hotspots_usecase.dart';
import '../../domain/usecases/get_wx_liver_list_usecase.dart';
import '../../domain/usecases/get_school_ranking_brand_uids_usecase.dart';
import '../controllers/study_abroad_controller.dart';
import '../controllers/school_ranking_controller.dart';

class StudyAbroadBinding extends Bindings {
  @override
  void dependencies() {
    // Data Sources
    Get.lazyPut<StudyAbroadRemoteDataSource>(
      () => StudyAbroadRemoteDataSourceImpl(dioClient: Get.find<DioClient>()),
      fenix: true,
    );

    // Repositories
    Get.lazyPut<StudyAbroadRepository>(
      () => StudyAbroadRepositoryImpl(
        remoteDataSource: Get.find<StudyAbroadRemoteDataSource>(),
      ),
      fenix: true,
    );

    // Use Cases
    Get.lazyPut<GetStudyAbroadHotspotsUseCase>(
      () => GetStudyAbroadHotspotsUseCase(Get.find<StudyAbroadRepository>()),
      fenix: true,
    );

    Get.lazyPut<GetWxLiverListUseCase>(
      () => GetWxLiverListUseCase(Get.find<StudyAbroadRepository>()),
      fenix: true,
    );

    Get.lazyPut<GetSchoolRankingBrandUidsUseCase>(
      () => GetSchoolRankingBrandUidsUseCase(Get.find<StudyAbroadRepository>()),
      fenix: true,
    );

    // Controller
    Get.lazyPut<StudyAbroadPageController>(
      () => StudyAbroadPageController(
        getStudyAbroadHotspotsUseCase:
            Get.find<GetStudyAbroadHotspotsUseCase>(),
        getWxLiverListUseCase: Get.find<GetWxLiverListUseCase>(),
      ),
      fenix: true,
    );

    Get.lazyPut<SchoolRankingController>(
      () => SchoolRankingController(
        getSchoolRankingBrandUidsUseCase:
            Get.find<GetSchoolRankingBrandUidsUseCase>(),
      ),
      fenix: true,
    );
  }
}
