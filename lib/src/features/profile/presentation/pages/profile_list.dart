import 'package:flutter/material.dart';
import 'package:get/get.dart' as GetX; // Using GetX
import 'package:get/get.dart';
import 'package:va_student_phone/app/module/user/views/persona_info_page.dart';
import 'package:va_student_phone/mine/class_comment/pages/class_comment_page.dart';
import 'package:va_student_phone/mine/common_question_page.dart';
import 'package:va_student_phone/mine/setting/feedback/feedback_page.dart';
import 'package:va_student_phone/resource/image_resource.dart';
import 'package:va_student_phone/src/core/mixins/auth_check_mixin.dart';
import 'package:va_student_phone/src/features/account_binding/presentation/pages/account_binding_screen.dart';

import '../../../../../component/common_web_page.dart';
import '../controllers/profile_controller.dart';

class ProfileList extends StatelessWidget with AuthCheckMixin {
  final ProfileController profileController;
  const ProfileList({super.key, required this.profileController});

  // 第一段列表项数据
  static const List<Map<String, dynamic>> firstListItems = [
    {'icon': ImageResource.profile_list_profile, 'title': '个人信息'},
    {'icon': ImageResource.profile_list_account_comment, 'title': '我的点评'},
    {'icon': ImageResource.profile_list_account, 'title': '账号管理'},
  ];

  // 第二段列表项数据
  static const List<Map<String, dynamic>> sectionListItems = [
    {'icon': ImageResource.profile_list_question, 'title': '常见问题'},
    {'icon': ImageResource.profile_list_message, 'title': '意见反馈'},
  ];

  // 提取的点击事件处理方法
  void handleItemTap(BuildContext context, int index, bool isFirstSection) {
    // 根据 isFirstSection 和 index 执行不同的导航逻辑
    if (isFirstSection) {
      switch (index) {
        case 0: // 个人信息
          Get.to(() => PersonaInfoPage(
                avatarUrl: profileController.currentUserAvatar.value,
              ));
          break;
        case 1: // 点评页面
          Get.to(() => ClassCommentPage());
          break;
        case 2: // 账号管理页面
          Get.to(() => AccountBindingScreen());
          break;
        default:
          break;
      }
    } else {
      switch (index) {
        case 0: // 常见问题页面
          final time = DateTime.now().millisecondsSinceEpoch;
          final webUrl =
              'https://download.visioneschool.com/client/app/app_web/index.html?params=$time';
          Get.to(() => CommonWebPage(webUrl: webUrl, title: '常见问题'));
          break;
        case 1: // 意见反馈页面
          Get.to(() => FeedBackPage());
          break;
        default:
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          SizedBox(height: 20),
          _buildSectionContainer(
            SizedBox(
              child: ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                // 防止内部 ListView 滚动，交给外部 Column 控制
                itemCount: firstListItems.length,
                itemBuilder: (context, index) {
                  final item = firstListItems[index];
                  return buildItem(
                    item,
                    index,
                    firstListItems.length,
                    () => checkLoginAndBind(() =>
                      handleItemTap(context, index, true)
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),
          _buildSectionContainer(
            SizedBox(
              child: ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(), // 防止内部 ListView 滚动
                itemCount: sectionListItems.length,
                itemBuilder: (context, index) {
                  final item = sectionListItems[index];
                  return buildItem(
                    item,
                    index,
                    sectionListItems.length,
                    () => checkLoginAndBind(() =>
                        handleItemTap(context, index, false)
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }

  // 抽取的复用方法，构建 ListTile 项
  Widget buildItem(Map<String, dynamic> item, int index, int itemCount,
      VoidCallback? onTap) {
    return Column(
      children: [
        ListTile(
          leading: Image.asset(
            item['icon'] as String,
            width: 20,
            height: 20,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Icon(Icons.error, color: Colors.red, size: 24);
            },
          ),
          title: Text(
            item['title'] as String,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF3C4258),
            ),
          ),
          trailing: const Icon(
            Icons.chevron_right,
            color: Colors.grey,
          ),
          onTap: onTap,
        ),
        if (index != itemCount - 1)
          const Divider(
            height: 0.5,
            indent: 16,
            endIndent: 16,
            color: Color(0xFFEEF2F5),
          ),
      ],
    );
  }

  Widget _buildSectionContainer(Widget content) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [content],
      ),
    );
  }
}
