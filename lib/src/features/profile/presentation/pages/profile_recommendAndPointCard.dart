import 'package:flutter/material.dart';
import 'package:va_student_phone/app/module/user/views/introduce_detail_page.dart';
import 'package:va_student_phone/app/module/user/views/poster_detail_page.dart';
import 'package:va_student_phone/resource/image_resource.dart';
import 'package:va_student_phone/src/core/mixins/auth_check_mixin.dart';
import 'package:get/get.dart';
import 'package:va_student_phone/src/features/profile/presentation/pages/profile_myPoints.dart';
import '../controllers/profile_controller.dart';

class RecommendAndPointCard extends StatelessWidget with AuthCheckMixin {
  const RecommendAndPointCard({Key? key}) : super(key: key);

  // 点击推荐有礼的卡片
  void _handleRecommendedGifts() {
    checkLoginAndBind(() => Get.to(() => IntroduceDetailPage()));
  }

  // 点击立即邀请按钮
  void _handleInviteNow() {
    checkLoginAndBind(() => Get.to(() => PosterDetailPage()));
  }

  // 点击推荐有礼的卡片
  void _handlePointsRedemptionGift() {}

  // 点击立即兑换按钮
  void _handleRedeemNow() {
    checkLoginAndBind(() => Get.to(() => ProfileMyPoints()));
  }

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find<ProfileController>();

    return Obx(
      () => Container(
        padding: const EdgeInsets.only(top: 20, left: 16, right: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 推荐有礼卡片（优化后）
            _buildCard(
              context: context,
              titleImage: ImageResource.profile_recommend_gift_title,
              statItems: [
                _buildReferralStat(
                    '有效推荐', profileController.effectiveReferrals.value ?? 0),
                _buildReferralStat(
                    '报名成功', profileController.successfulSignUps.value ?? 0),
              ],
              buttonText: '立即邀请',
              bottomText: '推荐好友一起学，豪礼拿不停',
              imageName: ImageResource.profile_recommend_gift_immediately,
              backgroundImage: ImageResource.profile_recommedCard_bgImage,
              onCardTap: _handleRecommendedGifts,
              onButtonTap: _handleInviteNow,
            ),
            SizedBox(width: 10),
            // 积分兑好礼卡片（优化后）
            _buildCard(
              context: context,
              titleImage: ImageResource.profile_exchange_gift_title,
              statItems: [
                _buildReferralStat(
                  '可用积分',
                  int.tryParse(profileController.points.value ?? '0') ?? 0,
                ),
              ],
              buttonText: '立即兑换',
              bottomText: '获得积分兑换精美好礼',
              imageName: ImageResource.profile_recommend_gift_exchange,
              backgroundImage: ImageResource.profile_exchangeCard_bgImage,
              onCardTap: _handlePointsRedemptionGift,
              onButtonTap: _handleRedeemNow,
            ),
          ],
        ),
      ),
    );
  }

  // 提取为可复用的卡片构建方法
  Widget _buildCard({
    required BuildContext context,
    required String titleImage,
    required List<Widget> statItems,
    required String buttonText,
    required String bottomText,
    required String imageName,
    required String backgroundImage,
    required VoidCallback onCardTap,
    required VoidCallback onButtonTap,
  }) {
    final double itemWidth = (MediaQuery.of(context).size.width - 42) / 2;
    final double itemHeight = 150;

    return Flexible(
      child: GestureDetector(
        onTap: onCardTap,
        child: Container(
          width: itemWidth,
          height: itemHeight,
          padding: EdgeInsets.all(10),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(backgroundImage),
              fit: BoxFit.fill,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5, bottom: 5),
                child: Image.asset(
                  titleImage,
                  height: 20,
                  fit: BoxFit.contain,
                ),
              ),
              Row(
                children: statItems,
              ),
              SizedBox(height: 10),
              _buildButton(buttonText, onButtonTap, imageName),
              SizedBox(height: 8),
              RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFF7B64B5),
                    fontWeight: FontWeight.w400,
                  ),
                  children: <TextSpan>[
                    TextSpan(text: bottomText),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 提取按钮构建方法
  Widget _buildButton(String btnTitle, VoidCallback onTap, String imageName) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(left: 0, bottom: 5),
        child: Stack(
          children: [
            Image.asset(
              // ImageResource.profile_recommend_gift_immediately,
              imageName,
              width: 110,
              height: 28,
              fit: BoxFit.fill,
            ),
            Positioned(
              left: 0,
              top: 0,
              right: 15,
              bottom: 0,
              child: Center(
                child: Container(
                  child: Text(
                    btnTitle,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralStat(String label, int count) {
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: 11,
            color: Color(0xFF7B64B5),
            fontWeight: FontWeight.w400,
          ),
          children: <TextSpan>[
            TextSpan(text: '$label '),
            TextSpan(
              text: '$count',
              style: TextStyle(
                color: Color(0xFF074CF2),
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
