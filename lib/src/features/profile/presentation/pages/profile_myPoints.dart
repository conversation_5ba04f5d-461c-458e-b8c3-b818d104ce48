import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:va_student_phone/app/module/user/views/introduce_detail_page.dart';
import 'package:va_student_phone/component/widget/common_app_bar.dart';
import 'package:va_student_phone/resource/image_resource.dart';
import 'package:get/get.dart';
import 'package:va_student_phone/src/features/profile/presentation/pages/profile_exchange_qrCode.dart';
import 'package:va_student_phone/src/features/profile/presentation/pages/profile_points_details.dart';

class ProfileMyPoints extends StatefulWidget {
  const ProfileMyPoints({Key? key}) : super(key: key);

  @override
  State<ProfileMyPoints> createState() => _ProfileMyPointsState();
}

class _ProfileMyPointsState extends State<ProfileMyPoints>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 模拟任务数据，实际可从接口等获取
  final List<Map<String, String>> taskList = [
    {"title": "首次绑定家校通", "desc": "微信搜索【唯寻国际教育服务号】，点击【家校通】登录后并完成绑定，即可获得2积分。", "imageStr": ImageResource.profile_myPoints_bindApp},
    {"title": "首次绑定唯寻网校APP", "desc": "登录【唯寻网校app】，手机号登录后，绑定学习账号，即可获得2积分。", "imageStr": ImageResource.profile_myPoints_bindApp},
    {"title": "推荐学员有礼", "desc": "每完成1个有效推荐即可获得50积分，推荐学员成功报名后可再得100积分。", "imageStr": ImageResource.profile_myPoints_recommendedGifts},
    {"title": "提交作业", "desc": "每完成一份作业，即可获得3积分。", "imageStr": ImageResource.profile_myPoints_uploadWork},
    {"title": "提交模考", "desc": "每完成一份模考，即可获得3积分。", "imageStr": ImageResource.profile_myPoints_examination},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 点击积分
  void _handlePointOnTap() {
    Get.to(() => ProfilePointsDetails());
  }

  // 二维码点击
  void _handleErCodeOnTap() {
    Get.to(() => ProfileExchangeQrCode());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F5F9),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              ImageResource.profile_myPoints_pageTopBgImage,
              width: double.infinity,
              height: 320,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) =>
                  Container(color: const Color(0xFFF4F5F9)),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                CommonAppBar(
                  title: '我的积分',
                  fontSize: 18,
                  backgroundColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            // color: Colors.white,
                            image: DecorationImage(
                              image: AssetImage(ImageResource.profile_mypoints_topCard_bgImage),
                              fit: BoxFit.fill,
                            ),
                            borderRadius: BorderRadius.circular(10),

                          ),
                          child: Stack(
                            children: [
                              // 中央内容区域
                              GestureDetector(
                                onTap: () => _handlePointOnTap(),
                                child: Padding(padding: EdgeInsets.only(left: 24, right: 30,), child: Container(
                                  width: double.infinity,
                                  padding:
                                  const EdgeInsets.symmetric(vertical: 24),
                                  child: Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            ImageResource
                                                .profile_myPoints_goldCoin,
                                            width: 32,
                                            height: 32,
                                            fit: BoxFit.fill,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            '120',
                                            style: TextStyle(
                                              fontSize: 36,
                                              fontWeight: FontWeight.w700,
                                              color: Color(0xFF3C4258),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Image.asset(
                                            ImageResource
                                                .profile_recommend_gift_rightArrow,
                                            width: 18,
                                            height: 18,
                                          ),
                                          Spacer(),
                                          GestureDetector(onTap: () => _handleErCodeOnTap(), child: Image.asset(ImageResource.profile_myPoints_defaultQrCode, width: 32, height: 32, fit: BoxFit.fill),),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        '完成任务得积分，精美礼品抢先兑',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: Color(0xFF6B7186),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 13),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 20, horizontal: 5),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TabBar(
                                  controller: _tabController,
                                  dividerColor: Color(0xFFE9EAEE),
                                  indicatorColor: Color(0xFFFDC72F),
                                  dividerHeight: 0.5,
                                  labelColor: Color(0xFF3C4258),
                                  labelStyle: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  unselectedLabelColor: Color(0xFF6B7186),
                                  unselectedLabelStyle: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tabs: const [
                                    Tab(text: '如何兑换'),
                                    Tab(text: '如何获得积分'),
                                    Tab(text: '最近上新'),
                                  ],
                                ),
                                SizedBox(height: 20),
                                Expanded(
                                  child: TabBarView(
                                    controller: _tabController,
                                    children: [
                                      _buildExchangeUI(),
                                      _buildGetPointsUI(),
                                      _buildGetPointsUI(),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 如何兑换
  Widget _buildExchangeUI() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '第一步：在学区积分柜操作面板选择自己喜欢的礼品，点击兑换',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF3C4258),
            ),
          ),
          SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 180,
            color: Color(0xFFECECEC),
            alignment: Alignment.center,
            child: Text(
              '图片占位',
              style: TextStyle(color: Color(0xFF999999), fontSize: 12),
            ),
          ),
          SizedBox(height: 24),
          Text(
            '第二步：打开唯寻网校app-我的-积分兑好礼-右上角二维码，对准机器扫描后即完成兑换',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF3C4258),
            ),
          ),
          SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 180,
            color: Color(0xFFECECEC),
            alignment: Alignment.center,
            child: Text(
              '图片占位',
              style: TextStyle(color: Color(0xFF999999), fontSize: 12),
            ),
          ),
          SizedBox(height: 24),
          Text(
            '如兑换过程中遇到问题请联系您的学管老师。',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF3C4258),
            ),
          ),
        ],
      ),
    );
  }

  // 如何获得积分
  Widget _buildGetPointsUI() {
    return ListView.builder(
      itemCount: taskList.length,
      itemBuilder: (context, index) {
        final task = taskList[index];
        return Container(
          padding: EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset(
                task["imageStr"] ?? "",
                width: 48,
                height: 48,
                fit: BoxFit.fill,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task["title"] ?? "",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF3C4258),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      task["desc"] ?? "",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF8F94A8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
