
#!/bin/bash
echo "iOS 上传至蒲公英"

echo "$PWD"

# 打包后的路径
filePath=../archive/release/Runner.ipa
#获取当前git用户名
name=$(git config user.name)
#获取当前git 分支
branch=$(git rev-parse --abbrev-ref HEAD)
# 获取git 提交记录
MESSAGE=$(git log -1 HEAD --pretty=format:%s)
echo "$MESSAGE"
## 上传至蒲公英
function upload_to_pyger() {
res=$(curl -F "file=@$filePath" \
-F "uKey=8eb23e2b81d76dbeebe3a73c53bfdc3d" \
-F "_api_key=11cad500a370a52876b9be4df16de982" \
-F "updateDescription=$MESSAGE" \
https://www.pgyer.com/apiv1/app/upload)

echo "[LOG] res $res"
key=$(echo "$res" | jq -r '.data.appKey')
url="https://www.pgyer.com/$key"
echo "[LOG] url $url"
qrcode=$(echo "$res" | jq -r '.data.appQRCodeURL')
echo "[LOG] qrcode $qrcode"
}


# 上传fir
function upload_to_fir() {

res=$(curl -X "POST" "http://api.bq04.com/apps" \
     -H "Content-Type: application/json" \
     -d "{\"type\":\"ios\", \"bundle_id\":\"com.weixun.studenteEducation\", \"api_token\":\"13581c4350ed341b416a26cd998f7064\"}")
echo "[LOG] res $res"
key=$(echo "$res" | jq -r '.cert.binary.key')
token=$(echo "$res" | jq -r '.cert.binary.token')
echo "[LOG] key $key"
echo "[LOG] token $token"

# 获取iOS的版本号和build
#version=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" ../ios/Runner/Info.plist)
#build=$(/usr/libexec/PlistBuddy -c "Print CFBundleVersion" ../ios/Runner/Info.plist)

# 获取应用程序的路径
app_path="../ios/Runner"

# 使用 PlistBuddy 命令获取版本号
version=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "${app_path}/Info.plist")

# 输出版本号
echo "版本号：${version}"


res2=$(curl   -F "key=$key"              \
       -F "token=$token"             \
       -F "file=@$filePath"            \
       -F "x:name=唯寻网校"             \
       -F "x:version=$version"         \
       -F "x:build=$build"               \
       -F "x:release_type=Adhoc"   \
       -F "x:changelog=$MESSAGE"       \
       https://upload.qbox.me)
echo "[LOG] res2 $res2"
}

upload_to_fir
#upload_to_pyger

#上传成功 发送钉钉通知
#
#token="1229cc01240f91e4f348b6f400cb3f85686b020caa4dca2cf71b975913ef27ae"
#Name="compute_04"
#
#curl 'https://oapi.dingtalk.com/robot/send?access_token='$token \
#    -H 'Content-Type: application/json' \
#    -d '{"msgtype": "markdown","markdown": {"title": "App 构建",
#                                            "text": "
#### App-唯寻网校-iOS-构建成功-Test\n\n
#> **构建人:** '"${name}"'\n\n
#> **分支:** '"${branch}"'\n\n
#> **修改记录:** '"${MESSAGE}"'\n\n
#> ![]('"$qrcode"')
#"}}'

