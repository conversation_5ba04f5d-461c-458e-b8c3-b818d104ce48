require 'net/http'
require 'uri'
require 'json'

default_platform(:ios)

# get latest commit message
def get_latest_commit_message()
    commit_message = `git log -1 --pretty=%B`
    return commit_message
end

def get_app_upload_token()
    url = URI("http://api.appmeta.cn/apps")
    http = Net::HTTP.new(url.host, url.port)
    request = Net::HTTP::Post.new(url)
    request["Content-Type"] = "application/json"
    request.body = "{\"type\":\"ios\", \"bundle_id\":\"com.weixun.studenteEducation\", \"api_token\":\"13581c4350ed341b416a26cd998f7064\"}"
    response = http.request(request)
    return response.body
end

def upload_to_fir()
  response=JSON.parse(get_app_upload_token())
  upload_key = response['cert']['binary']['key']
  puts "upload_key=#{upload_key}"
  upload_token = response['cert']['binary']['token']
  puts "upload_token=#{upload_token}"
  upload_url = response['cert']['binary']['upload_url']
  puts "upload_url=#{upload_url}"
  uri = URI('https://upload.qbox.me/')
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true

  request = Net::HTTP::Post.new(uri.path)
  request['content-type'] = 'multipart/form-data'

  file = File.open('../archive/release/Runner.ipa', 'rb')
  version = get_version_number(xcodeproj: "Runner.xcodeproj")
  build = get_build_number(xcodeproj: "Runner.xcodeproj")
  puts "version=#{version}"
  puts "build=#{build}"
  request.set_form([
    ['key', upload_key],
    ['token', upload_token],
    ['file', file],
    ['x:name', '唯寻网校'],
    ['x:version', version],
    ['x:build', build],
    ['x:changelog', get_latest_commit_message()],
    ['x:release_type', 'Adhoc']
  ], 'multipart/form-data')

  response = http.request(request)

  if response.code == '200'
    puts response.body
    send_dingtalk_msg(response.body, get_latest_commit_message())
  else
    puts "请求失败,状态码: #{response.code}"
  end
end



def upload_to_pgyer()
 commit_message = get_latest_commit_message()
 res = pgyer(api_key: "11cad500a370a52876b9be4df16de982")
 send_dingtalk_msg(res['buildQRCodeURL'], commit_message)
end

 #  send dingtalk message use http
def send_dingtalk_msg(buildQRCodeURL, commit_message)
  puts "send_dingtalk_msg"
 webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=1229cc01240f91e4f348b6f400cb3f85686b020caa4dca2cf71b975913ef27ae'
 # 构建请求体
#  ![](https://bigshot.oss-cn-shanghai.aliyuncs.com/icons/ic_ios.png)
 payload = {
   msgtype: 'markdown',
   markdown: {
     title: '唯寻网校App-iOS',
     text: <<~EOF
          # 唯寻网校iOS
          - **构建分支** #{`git rev-parse --abbrev-ref HEAD`.strip}
          - **更新信息** #{commit_message}
          - **构建时间** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
          ![](#{buildQRCodeURL})
        EOF
   }
 }

 # 将请求体转换为 JSON
 request_body = payload.to_json
 uri = URI.parse(webhook_url)
 # 创建 HTTP 对象
 http = Net::HTTP.new(uri.host, uri.port)
 http.use_ssl = true # 启用 SSL
 # 构建请求
 request = Net::HTTP::Post.new(uri.request_uri)
 request['Content-Type'] = 'application/json'
 request.body = request_body
 # 发送请求
 response = http.request(request)
 puts "response=====#{response.code}"
 if response.code == "200"
 else
   puts response.code
 end
end

platform :ios do
  desc "发布 蒲公英"
  lane :beta do
      puts "以 ad-hoc 方式打包"
      #match(type: "adhoc",force_for_new_devices: true)
      gym(
        export_method: "ad-hoc",
        scheme: "Runner",
        output_directory: "./archive/release/",
        configuration: "Release",
        clean: true,
        export_options: {
          method: "ad-hoc",
          provisioningProfiles: {
            "com.weixun.studenteEducation": "match AdHoc com.weixun.studenteEducation"
            }
        },
      )
#       upload_to_fir()
      upload_to_pgyer()
  end
   desc "发布 AppStore"
   lane :upload do
    puts "以 app-store 方式打包"
    match(type: "appstore")
    gym(
      export_method: "app-store",
      scheme: "Runner",
      output_directory: "./archive/release/",
      configuration: "Release",
      clean: true
    )
     puts "上传AppStore Connect"
     deliver(
        ipa: "./archive/release/Runner.ipa",
        skip_screenshots: true,
        skip_metadata: true,
        skip_app_version_update: true
     )
   end
end
