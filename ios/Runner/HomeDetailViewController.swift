//
//  HomeDetailViewController.swift
//  Runner
//
//  Created by kevin on 7/4/24.
//  Copyright © 2024 The Chromium Authors. All rights reserved.
//
import UIKit
import WebKit

class HomeDetailViewController: UIViewController, WKNavigationDelegate, WKUIDelegate {
    var channel:FlutterMethodChannel = FlutterMethodChannel()
    var result: FlutterResult!
    var urlString:String = "";
    var javaScriptString:String = "";
    var navTitle:String = ""
    var webView = WKWebView()
    var titleArray:Array = [String]()
    
    override func viewWillAppear(_ animated: Bool) {
        // 在这里设置导航栏颜色
        super.viewWillAppear(animated)
         self.navigationController?.setNavigationBarHidden(false, animated: false)///导航栏显示
        //        self.navigationController?.navigationBar.isTranslucent = false
        let attrDic = [NSAttributedString.Key.foregroundColor: UIColor.black,
                       NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18)]
        
        if #available(iOS 13.0, *) {
            let barApp = UINavigationBarAppearance()
            barApp.backgroundColor = .white
            //基于backgroundColor或backgroundImage的磨砂效果
            barApp.backgroundEffect = nil
            //阴影颜色（底部分割线），当shadowImage为nil时，直接使用此颜色为阴影色。如果此属性为nil或clearColor（需要显式设置），则不显示阴影。
            //barApp.shadowColor = nil;
            //标题文字颜色
            barApp.titleTextAttributes = attrDic
            navigationController?.navigationBar.scrollEdgeAppearance = barApp
            navigationController?.navigationBar.standardAppearance = barApp
        }else {
            navigationController?.navigationBar.titleTextAttributes = attrDic
          let navBgImg = createWhiteImage(size: CGSize(width: UIApplication.shared.keyWindow!.bounds.size.width, height: UIApplication.shared.keyWindow!.safeAreaInsets.top+64))
                                navigationController?.navigationBar.shadowImage = UIImage()
                       navigationController?.navigationBar.setBackgroundImage(navBgImg, for: .default)
        }
        //透明设置
        navigationController?.navigationBar.isTranslucent = false;
        //navigationItem控件的颜色
        navigationController?.navigationBar.tintColor = .black
        if #available(iOS 11.0, *) {
            webView.scrollView.contentInsetAdjustmentBehavior = UIScrollView.ContentInsetAdjustmentBehavior.never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
    }
    
    // WKNavigationDelegate method
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        let js = javaScriptString
        webView.evaluateJavaScript(js) { (result, error) in
            if let error = error {
                print("Error writing token to localStorage: \(error)")
            } else {
                print("Token successfully written to localStorage")
            }
        }
        
    }
    
    func createWhiteImage(size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        let whiteColor = UIColor.white.cgColor
        UIGraphicsGetCurrentContext()?.setFillColor(whiteColor)
        UIGraphicsGetCurrentContext()?.fill(CGRect(origin: .zero, size: size))
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    deinit {
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "WebViewBridge")
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        //        navigationController?.navigationBar.barTintColor = UIColor.white
        //        navigationController?.navigationBar.tintColor = UIColor.white
        navigationController?.navigationBar.titleTextAttributes = [NSAttributedString.Key.foregroundColor:UIColor.darkGray]
        //        navigationController?.navigationBar.isTranslucent = false
        view.backgroundColor = UIColor.white
        self.navigationItem.title = navTitle
        navigationController?.navigationBar.setBackgroundImage(nil, for: .default)
        navigationController?.navigationBar.shadowImage = nil
        
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(named: "NavigationBar_goBack_icon"), for: .normal)
        backButton.bounds = CGRect(x: 0, y: 0, width: 80, height: 40)
        backButton.contentHorizontalAlignment = .left
        backButton.addTarget(self, action: #selector(backAction), for: .touchUpInside)
        let backView = UIBarButtonItem(customView: backButton)
        //        let barButtonItem = UIBarButtonItem(barButtonSystemItem: .fixedSpace, target: nil, action: nil)
        //        barButtonItem.width = -5
        navigationItem.leftBarButtonItem = backView
        // 创建配置
        let config = WKWebViewConfiguration()
        // 创建UserContentController（提供JavaScript向webView发送消息的方法）
        let userContent = WKUserContentController()
        //添加消息处理，注意：self指代的对象需要遵守WKScriptMessageHandler协议，结束时需要移除
        userContent.add(self, name: "WebViewBridge")
        // 将UserConttentController设置到配置文件
        config.userContentController = userContent
        var safeAreaInsetsBottom:CGFloat = 0
        var safeAreaInsetsTop:CGFloat = 0
        if #available(iOS 11.0, *) {
            safeAreaInsetsBottom  = UIApplication.shared.keyWindow?.safeAreaInsets.bottom ?? 0
            safeAreaInsetsTop  = (UIApplication.shared.keyWindow?.safeAreaInsets.bottom)! > 0 ? 88:  64
        } else {
            safeAreaInsetsTop = 64
            // Fallback on earlier versions
        };
        // 高端的自定义配置创建WKWebView
        webView = WKWebView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.size.width, height: UIScreen.main.bounds.size.height - safeAreaInsetsBottom-safeAreaInsetsTop), configuration: config)
        webView.backgroundColor = .white
        // 设置访问的URL
        let url = URL(string: urlString)
        // 根据URL创建请求
        let requst = URLRequest(url: url!)
        // 设置代理
        webView.navigationDelegate = self
        webView.uiDelegate = self
        // WKWebView加载请求
        webView.load(requst)
        // 将WebView添加到当前view
        view.addSubview(webView)
//        NotificationCenter.default.addObserver(self, selector: #selector(applicationBeco/*meActive), name: UIApplication.NSNotification.Name.UIApplicationDidBecomeActive, object: nil)*/
    }
    @objc func applicationBecomeActive() {
        //       let vc = WBSignViewController()
        //       let nav = UINavigationController.init(rootViewController: vc)
        //       nav.modalPresentationStyle = .overFullScreen
        //       self.present(nav, animated: false, completion: nil)
    }
    open func connectToWeb(javaScriptString:String){
        webView.evaluateJavaScript(javaScriptString) { (response, error) in
        }
    }
    @objc func backAction() {
        
        if (self.urlString.hasPrefix("https://work.visioneschool.com")||self.urlString.hasPrefix("https://p-work.visioneschool.com")||self.urlString.hasPrefix("https://t-work.visioneschool.com")||self.urlString.hasPrefix("https://d-work.visioneschool.com")) {
            navigationController?.popViewController(animated: true)
            return
        }
        if webView.canGoBack {
            if titleArray.count < 1 {
                navigationController?.popViewController(animated: true)
                return
            }
            titleArray.remove(at: titleArray.count - 1);
            if titleArray.count >= 1 {
                self.navigationItem.title = titleArray[titleArray.count - 1];
            }else{
                self.navigationItem.title = navTitle;
            }
            webView.goBack()
        }else{
            navigationController?.popViewController(animated: true)
        }
    }
}

extension HomeDetailViewController:WKScriptMessageHandler{
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage)  {
        if message.name == "WebViewBridge" {
            guard let messageString:String = message.body as? String else {
                return
            }
            guard let jsonData:Data = messageString.data(using: .utf8) else {
                return
            }
            if let dict = try? JSONSerialization.jsonObject(with: jsonData,
                                                            options: .mutableContainers) as? [String : Any] {
                if dict?["type"] as! String == "getToken" {
                    webView.evaluateJavaScript(self.javaScriptString) { (response, error) in
                    }
                }else if dict?["type"] as! String == "jump"{
                    let data:Dictionary<String,String> = dict?["data"] as! Dictionary<String, String>
                    guard let title = data["title"] else {
                        return
                    }
                    self.navigationItem.title = title;
                    titleArray.append(title)
                }else if dict?["type"] as! String == "back"{
                    if titleArray.count >= 1 {
                        titleArray.remove(at: titleArray.count - 1);
                        //                        self.navigationItem.title = titleArray[titleArray.count - 1];
                        if titleArray.count >= 1 {
                            self.navigationItem.title = titleArray[titleArray.count - 1];
                        }else{
                            self.navigationItem.title = navTitle;
                        }
                    }
                }else{
                    if dict?["type"] as! String == "logOut"{
                        navigationController?.popViewController(animated: true)
                    }
                    if (result != nil) {
                        result(messageString)
                    }
                }
            }
        }
    }
}
