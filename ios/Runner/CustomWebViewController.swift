//
//  CustomWebViewController.swift
//  Runner
//
//  Created by kevin on 7/1/24.
//  Copyright © 2024 The Chromium Authors. All rights reserved.
//
import UIKit
import WebKit

class WebViewController: UIViewController, WKNavigationDelegate, WKScriptMessageHandler {
    var webView: WKWebView!
    var urlString: String?
    var titleArray: [String] = ["Web View"]  // 初始化标题数组
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 配置 WebView
        let contentController = WKUserContentController()
        contentController.add(self, name: "WebViewBridge")
        
        let config = WKWebViewConfiguration()
        config.userContentController = contentController
        
        webView = WKWebView(frame: self.view.frame, configuration: config)
        webView.navigationDelegate = self
        self.view.addSubview(webView)
        
        if let urlString = urlString, let url = URL(string: urlString) {
            let request = URLRequest(url: url)
            webView.load(request)
        }
        
        self.title = titleArray.last  // 设置初始标题
        //        let backButton = UIBarButtonItem(title: "Back", style: .plain, target: self, action: #selector(back))
        //        self.navigationItem.leftBarButtonItem = backButton
        setupBackButton()
    }
    
    func setupBackButton() {
        let backButton: UIBarButtonItem
        if #available(iOS 13.0, *) {
            let config = UIImage.SymbolConfiguration(pointSize: 18, weight: .regular)
            let backButtonImage = UIImage(systemName: "chevron.backward", withConfiguration: config)
            backButton = UIBarButtonItem(image: backButtonImage, style: .plain, target: self, action: #selector(back))
        } else {
            if let customBackButtonImage = UIImage(named: "back_arrow") {
                backButton = UIBarButtonItem(image: customBackButtonImage, style: .plain, target: self, action: #selector(back))
            } else {
                backButton = UIBarButtonItem(title: "Back", style: .plain, target: self, action: #selector(back))
            }
        }
        backButton.tintColor = .black
        self.navigationItem.leftBarButtonItem = backButton
    }
    
    @objc func back() {
        if webView.canGoBack {
            webView.goBack()
            // 弹出最后一个标题
            if titleArray.count > 1 {
                titleArray.removeLast()
                self.title = titleArray.last
            }
        } else {
            if let navigationController = self.navigationController {
                navigationController.popViewController(animated: true)
            } else {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }
    
    // WKNavigationDelegate method
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        let js = "localStorage.setItem('tokensss', 'dssssssssssss23112121');"
        webView.evaluateJavaScript(js) { (result, error) in
            if let error = error {
                print("Error writing token to localStorage: \(error)")
            } else {
                print("Token successfully written to localStorage")
            }
        }
        
    }
    
    // 实现 WKScriptMessageHandler 协议方法
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        print("Received title from JS: \(message.body)")
        if message.name == "WebViewBridge", let messageBody = message.body as? String {
            // Convert the message body to Data
            if let data = messageBody.data(using: .utf8) {
                do {
                    // Parse the JSON data
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        // Handle the JSON data
                        if let type = json["type"] as? String, type == "jump",
                           let data = json["data"] as? [String: Any],
                           let title = data["title"] as? String,
                           let url = data["url"] as? String {
                            print("Type: \(type), Title: \(title), URL: \(url)")
                            // Perform necessary actions with the parsed data
                            titleArray.append(title)
                            self.title = title;
                        }
                    }
                } catch {
                    print("Error parsing JSON: \(error)")
                }
            }
        }
    }
}

