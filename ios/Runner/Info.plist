<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>com.weixun.studenteEducation</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>zh_CN</string>
		<key>CFBundleDisplayName</key>
		<string>唯寻网校</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>唯寻网校</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string></string>
				<key>CFBundleURLSchemes</key>
				<array/>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>weixin</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>wxd46d331dd71a222a</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>GrowingIO</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>growing.8c4c9c6df86a60b9</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>classin</string>
			<string>weixin</string>
			<string>weixinULAPI</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>NSAppleMusicUsageDescription</key>
		<string>唯寻网校需要获取您的媒体资料库权限，用来上传答题卡文件</string>
		<key>NSCalendarsUsageDescription</key>
		<string>唯寻网校课程需要使用您的日历权限，查看课表</string>
		<key>NSCameraUsageDescription</key>
		<string>唯寻网校需要获取您的相机权限，用来上传答题卡图片</string>
		<key>NSContactsUsageDescription</key>
		<string>唯寻网校需要使用您的通讯录权限</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>唯寻网校个人信息模块、签到功能需要使用定位获取当前位置</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>唯寻网校需要使用您的麦克风权限，用来口语题目录音</string>
		<key>NSLocationDefaultAccuracyReduced</key>
		<string>true</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>唯寻网校个人信息模块、签到功能需要使用定位获取当前位置</string>
		<key>NSMotionUsageDescription</key>
		<string>唯寻网校需要使用您的运动与健康数据权限</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>唯寻网校需要获取您的相册权限，用来保存图片</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>唯寻网校需要获取您的相册权限，用来上传答题卡图片</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>唯寻网校需要使用您的麦克风权限，用来口语题目录音</string>
		<key>UIApplicationSceneManifest</key>
		<dict>
			<key>UIApplicationSupportsMultipleScenes</key>
			<false/>
		</dict>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>io.flutter.embedded_views_preview</key>
		<string>YES</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
	</dict>
</plist>
