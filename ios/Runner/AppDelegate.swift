import UIKit
import Flutter
import UserNotifications
import PushKit
import AVFoundation
import AuthenticationServices
import CoreLocation

let buglyAppId :String = "b8d57da75b";
let buglyAppKey :String = "4bb79470-094d-4fdb-8eec-b49e1f8a2f13";

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate,ASAuthorizationControllerDelegate,ASAuthorizationControllerPresentationContextProviding{
    
    var channel:FlutterMethodChannel?
    let CHANNEL:String = "com.wei_xun.student_education"
    var landspace:NSInteger = 0;//0:默认的强制竖屏  1:强制横屏  2:横竖屏切换
    // 定义一个 bool 类型的变量
    var isFullScreen: Bool = false
    
    var controller : FlutterViewController?
    var navigationController: UINavigationController?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        controller  = window?.rootViewController as? FlutterViewController;
        self.navigationController = UINavigationController.init(rootViewController: controller!)
        self.window = UIWindow.init(frame: UIScreen.main.bounds)
        self.window?.rootViewController = self.navigationController
        self.navigationController?.setNavigationBarHidden(true, animated: true)
        self.window?.makeKeyAndVisible()
        
        channel = FlutterMethodChannel.init(name: CHANNEL, binaryMessenger: controller!.binaryMessenger)
        channel?.setMethodCallHandler{(call,result)-> Void in
            if(call.method == "getClientId"){
                result("clientId")
            }else if(call.method == "openNotification"){
                self.openNotification();
            }else if(call.method == "checkNotificationStatus")
            {
                if #available(iOS 10.0, *) {
                    UNUserNotificationCenter.current().getNotificationSettings { (set) in
                        if set.authorizationStatus == UNAuthorizationStatus.notDetermined{
                            //                            print("推送不允许")
                            result(false);
                        }else if set.authorizationStatus == UNAuthorizationStatus.denied{
                            //                            print("推送不允许")
                            result(false);
                        }else if set.authorizationStatus == UNAuthorizationStatus.authorized{
                            //                            print("推送允许")
                            result(true);
                        }
                    }
                } else {
                    let ty = UIApplication.shared.currentUserNotificationSettings?.types
                    if Int(ty!.rawValue) == 0{
                        //                        print("用户不允许推送")
                        result(false);
                        
                    }else{
                        //                        print("用户允许推送")
                        result(true);
                    }
                }
            }else if(call.method == "uploadError"){
            }else if(call.method == "DeviceOrientationLandsRight"){//强制横屏
                self.switchOrientation(isFullScreen: true)
                self.landspace = 1;
            }else if(call.method == "DeviceOrientationportraitUp"){//强制竖屏
                self.switchOrientation(isFullScreen: false)
                self.landspace = 0;
            }else if(call.method == "DeviceOrientationportraitUp|right"){//横竖屏切换
                self.landspace = 2;
            }else if(call.method == "availableios13"){
                if #available(iOS 13.0, *) {
                    result(true);
                }else{
                    result(false);
                }
            }else if(call.method == "AuthorizationAppleIDAction"){
                self.handleAuthorizationAppleIDButtonPress();
                result("");
            }else if(call.method == "openOtherApp"){
                let dict = call.arguments as! Dictionary<String, String>;
                let url:String = dict["url"] ?? "";
                //                print("call.arguments +",url);
                self.openOtherApp(urlString: url);
            }else if(call.method == "openSetting"){
                //                print("AAAAAAAA"+call.method)
                let url = NSURL.init(string: UIApplicationOpenSettingsURLString)
                if(UIApplication.shared.canOpenURL(url! as URL)) {
                    UIApplication.shared.openURL(url! as URL)
                }
            }else if(call.method == "checkLocationIsOpen"){
                if CLLocationManager.authorizationStatus() == CLAuthorizationStatus.notDetermined {
                    result(0)
                } else if CLLocationManager.authorizationStatus() == CLAuthorizationStatus.denied {
                    result(2)
                } else{
                    result(1)
                }
            }else if (call.method == "openWebView"){
                let arguments = call.arguments as! NSDictionary
                if arguments["url"] as! String == ""{
                    if (self.navigationController?.viewControllers.count)! > 1 {
                        self.navigationController?.popToRootViewController(animated: true)
                    }
                    return
                }
                let url = arguments["url"] ?? ""
                let title = arguments["title"] ?? ""
                let javaScriptString = arguments["javaScriptString"] ?? ""
                self.goToNativePage(result: result, title: title as! String, url: url as! String, javaScriptString: javaScriptString as! String, channel: self.channel!)
            }
        }
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func goToNativePage(result: FlutterResult,title: String,url: String,javaScriptString: String,channel: FlutterMethodChannel) {
        let detailVC: HomeDetailViewController = HomeDetailViewController()
//        设置导航栏颜色
//        detailVC.navigationController?.navigationBar.barTintColor = UIColor.white
        detailVC.navTitle = title
        detailVC.urlString = url
        detailVC.javaScriptString = javaScriptString
//        detailVC.result = result
//        detailVC.channel = channel
//        vc = detailVC
        self.navigationController?.topViewController?.navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func openOtherApp(urlString:String) {
        if let url = URL(string: urlString) {
            let isOpen =  UIApplication.shared.canOpenURL(url);
            if isOpen {
                //根据iOS系统版本，分别处理
                if #available(iOS 10, *) {
                    UIApplication.shared.open(url, options: [:],
                                              completionHandler: {
                        (success) in
                    })
                } else {
                    UIApplication.shared.openURL(url)
                }
            }else{
                channel?.invokeMethod("callBackOpenError", arguments: "0")
            }
        }
    }
    
    @objc func handleAuthorizationAppleIDButtonPress() {
        
        if #available(iOS 13.0, *) {
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            let request = appleIDProvider.createRequest()
            request.requestedScopes = [.fullName, .email]
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = self
            authorizationController.presentationContextProvider = self
            authorizationController.performRequests()
        } else {
            // Fallback on earlier versions
        }
        
    }
    
    @available(iOS 13.0, *)
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            channel?.invokeMethod("onReceiveAppIdUid", arguments: appleIDCredential.user)
        }else if let passwordCredential = authorization.credential as? ASPasswordCredential{
            channel?.invokeMethod("onReceiveAppIdUid", arguments: passwordCredential.user)
        }
    }
    
    @available(iOS 13.0, *)
    
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        guard let error = error as? ASAuthorizationError else {
            return
        }
        switch error.code {
        case .canceled:
            // user press "cancel" during the login prompt
            print("Canceled")
            channel?.invokeMethod("onReceiveAPPIdError", arguments:"取消登录")
        case .unknown:
            // user didn't login their Apple ID on the device
            print("Unknown")
            channel?.invokeMethod("onReceiveAPPIdError", arguments:"苹果登录发生未知错误")
        case .invalidResponse:
            // invalid response received from the login
            print("Invalid Respone")
            channel?.invokeMethod("onReceiveAPPIdError", arguments:"苹果登录授权请求响应无效")
        case .notHandled:
            // authorization request not handled, maybe internet failure during login
            print("Not handled")
            channel?.invokeMethod("onReceiveAPPIdError", arguments:"苹果登录未能处理授权请求")
            
        case .failed:
            // authorization failed
            print("Failed")
            channel?.invokeMethod("onReceiveAPPIdError", arguments:"苹果登录授权请求失败")
        @unknown default:
            print("Default")
        }
        
        
        //        if error.self == AuthorizationErrorCode
        
        //
    }
    
    /// MARK: ASAuthorizationControllerPresentationContextProviding
    @available(iOS 13.0, *)
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        
        return self.window!
        
    }
    
    
    ///跳转到设置界面
    func openNotification(){
        let urlObj = URL(string:UIApplicationOpenSettingsURLString)
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(urlObj! as URL, options: [ : ], completionHandler: { Success in
            })} else {
                UIApplication.shared.openURL(urlObj!)
            }
    }
    
    //注册通知
    func registerRemoteNotification() {
        let systemVer = (UIDevice.current.systemVersion as NSString).floatValue;
        if systemVer >= 10.0 {
            if #available(iOS 10.0, *) {
                let center:UNUserNotificationCenter = UNUserNotificationCenter.current()
                center.delegate = self;
                center.requestAuthorization(options: [.alert,.badge,.sound], completionHandler: { (granted:Bool, error:Error?) -> Void in
                    if (granted) {
                        NSLog("注册通知成功")  // 点击允许
                    } else {
                        NSLog("注册通知失败")  // 点击不允许
                    }
                })
                UIApplication.shared.registerForRemoteNotifications()
                return;
            }
        }
        
        if systemVer >= 8.0 {
            if #available(iOS 8.0, *) {
                let userSettings = UIUserNotificationSettings(types: [.badge, .sound, .alert], categories: nil)
                UIApplication.shared.registerUserNotificationSettings(userSettings)
                UIApplication.shared.registerForRemoteNotifications()
            }
        }
    }
    
    override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        // 警告: 需要重写当前方法，gtsdk的接管系统方法就会生效，否则会影响回执
        // 保持空实现
    }
    
    override func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        //        if (landspace == 0){
        //            return UIInterfaceOrientationMask.portrait
        //        }else if(landspace == 1){
        //            return  UIInterfaceOrientationMask.landscapeRight;
        //        }else{
        //            return UIInterfaceOrientationMask.all;
        //        }
        if isFullScreen {
            if #available(iOS 16.0, *) {
                // 16 及以上可以做到根据屏幕方向适配横屏
                return .landscape
            } else {
                // 16 以下不方便做, 所以我们是强制 右横屏
                return .landscapeRight
            }
        }
        return .portrait
    }
    
    func switchOrientation(isFullScreen: Bool) {
        
        let kAppdelegate = UIApplication.shared.delegate as? AppDelegate
        
        kAppdelegate?.isFullScreen = isFullScreen
        
        // 设置屏幕为横屏
        if #available(iOS 16.0, *) {
            controller!.setNeedsUpdateOfSupportedInterfaceOrientations()
            
            guard let scence = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
                return
            }
            
            let orientation: UIInterfaceOrientationMask = isFullScreen ? .landscape : .portrait
            
            let geometryPreferencesIOS = UIWindowScene.GeometryPreferences.iOS(interfaceOrientations: orientation)
            scence.requestGeometryUpdate(geometryPreferencesIOS) { error in
                print("强制\(isFullScreen ? "横屏" : "竖屏" )错误: \(error)")
            }
            
        } else {
            
            let oriention: UIDeviceOrientation = isFullScreen ? .landscapeRight : .portrait
            
            UIDevice.current.setValue(oriention.rawValue, forKey: "orientation")
            
            UIViewController.attemptRotationToDeviceOrientation()
        }
    }
}
