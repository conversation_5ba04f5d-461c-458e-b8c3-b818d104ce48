<?xml version="1.0" encoding="UTF-8"?>
<svg width="76px" height="76px" viewBox="0 0 76 76" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" gradientTransform="translate(0.500000,0.500000),rotate(90.000000),scale(1.000000,1.009929),translate(-0.500000,-0.500000)" id="radialGradient-1">
            <stop stop-color="#FFC3A4" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
        <rect id="path-2" x="0" y="0" width="56" height="56" rx="20"></rect>
        <filter x="-27.7%" y="-25.9%" width="155.4%" height="155.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.879047383   0 0 0 0 0.899073366   0 0 0 0 0.932450004  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-82.1%" y="-82.9%" width="281.1%" height="268.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.777341879   0 0 0 0 0.670679306  0 0 0 0.696452961 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.3151258%" id="linearGradient-5">
            <stop stop-color="#FF5B00" offset="0%"></stop>
            <stop stop-color="#EC1313" offset="100%"></stop>
        </linearGradient>
        <path d="M4.01448076,0.0145332774 L22.0000262,0.0796447917 C24.2084896,0.0893178271 25.9925878,1.8856271 25.9871968,4.09410506 L25.9178319,25.7650981 C25.9108822,27.9363256 24.1730904,29.7055441 22.0023361,29.7514026 L4.08448374,30.1299289 C1.87583753,30.1765879 0.0475513658,28.4239511 0.000892287212,26.2153049 C0.000297445658,26.1871476 2.8463349e-17,26.1589847 0,26.1308211 L0,4.01450707 C-2.705415e-16,1.80536807 1.790861,0.0145070658 4,0.0145070658 C4.00482693,0.0145070658 4.00965386,0.014515803 4.01448076,0.0145332774 Z" id="path-6"></path>
        <filter x="-1.9%" y="-1.7%" width="103.8%" height="103.3%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.687365139   0 0 0 0 0.161354921  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M8.6,3.9 L24,3.9 C26.209139,3.9 28,5.690861 28,7.9 L28,27.85 C28,30.059139 26.209139,31.85 24,31.85 L8.6,31.85 C6.390861,31.85 4.6,30.059139 4.6,27.85 L4.6,7.9 C4.6,5.690861 6.390861,3.9 8.6,3.9 Z" id="path-9"></path>
        <filter x="-8.5%" y="-3.6%" width="117.1%" height="114.3%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.509229972   0 0 0 0 0.350367997  0 0 0 0.726988466 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="49.2583419%" y1="0.00761524218%" x2="50.7314992%" y2="99.3076149%" id="linearGradient-11">
            <stop stop-color="#FF5B00" offset="0%"></stop>
            <stop stop-color="#F23737" offset="98.5729724%"></stop>
        </linearGradient>
        <filter x="-29.3%" y="-24.3%" width="158.6%" height="148.6%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M8.6,3.9 L24,3.9 C26.209139,3.9 28,5.690861 28,7.9 L28,27.85 C28,30.059139 26.209139,31.85 24,31.85 L8.6,31.85 C6.390861,31.85 4.6,30.059139 4.6,27.85 L4.6,7.9 C4.6,5.690861 6.390861,3.9 8.6,3.9 Z" id="path-13"></path>
        <filter x="-4.3%" y="-3.6%" width="108.5%" height="107.2%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.968627451   0 0 0 0 0.270588235   0 0 0 0 0.133333333  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M6.10126495,6.27490997 C16.348671,5.95967356 22.8573634,5.95967356 25.6273422,6.27490997 C26.1242193,12.0354055 26.1242193,19.9749741 25.6273422,30.093616 L9.90392575,29.7671543 C7.71848544,29.7217785 5.97420229,27.9304205 5.9870278,25.7445468 L6.10126495,6.27490997 L6.10126495,6.27490997 Z" id="path-15"></path>
        <filter x="-20.0%" y="-8.3%" width="139.9%" height="133.3%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.659540559   0 0 0 0 0.0656279773  0 0 0 0.595231063 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-102.6%" y="-51.3%" width="305.1%" height="202.6%" filterUnits="objectBoundingBox" id="filter-17">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-102.000000, -224.000000)">
            <g id="编组-17" transform="translate(8.000000, 224.000000)">
                <g id="编组-8" transform="translate(94.000000, 0.000000)">
                    <g id="编组-7" transform="translate(10.000000, 10.000000)">
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <rect stroke="#FFFFFF" stroke-width="1" stroke-linejoin="square" fill="url(#radialGradient-1)" fill-rule="evenodd" x="0.5" y="0.5" width="55" height="55" rx="20"></rect>
                        </g>
                        <g id="课程" filter="url(#filter-4)" transform="translate(14.000000, 12.000000)">
                            <g id="编组-9">
                                <g id="路径-9">
                                    <mask id="mask-7" fill="white">
                                        <use xlink:href="#path-6"></use>
                                    </mask>
                                    <g id="蒙版">
                                        <use fill="url(#linearGradient-5)" fill-rule="evenodd" xlink:href="#path-6"></use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-6"></use>
                                    </g>
                                    <polygon fill="#FFFFFF" opacity="0.174386887" mask="url(#mask-7)" points="26 1.3 0 31 0 0.611630124 26.7147493 -0.2"></polygon>
                                </g>
                                <g id="矩形" opacity="0.71161034">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                                    <use fill-opacity="0.6" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-9"></use>
                                </g>
                                <path d="M6.94354918,5.39616775 C19.6885646,5.96811549 26.2789647,6.30066078 26.7147493,6.39380362 C26.6140273,6.35424964 26.6140273,14.2521411 26.7147493,30.0874781 L10.3474696,30.092382 C8.13833069,30.0930438 6.3469332,28.3027195 6.34627131,26.0935806 C6.34625963,26.0546042 6.34681764,26.0156298 6.34794515,25.9766697 L6.94354918,5.39616775 L6.94354918,5.39616775 Z" id="矩形" fill="url(#linearGradient-11)" filter="url(#filter-12)" transform="translate(16.471775, 17.744892) scale(-1, 1) translate(-16.471775, -17.744892) "></path>
                                <g id="矩形" opacity="0.299905686">
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-13"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                                </g>
                                <g id="矩形" opacity="0.171210153" transform="translate(15.981953, 18.066049) scale(-1, 1) translate(-15.981953, -18.066049) ">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-15"></use>
                                </g>
                                <path d="M18.25,2.6 L24.1,2.6 L24.1,12.3333917 C24.1,12.8856764 23.6522847,13.3333917 23.1,13.3333917 C22.8882585,13.3333917 22.6819819,13.2661807 22.5108845,13.1414405 L21.175,12.1675015 L21.175,12.1675015 L19.8391155,13.1414405 C19.3928425,13.4668 18.7673107,13.3687803 18.4419512,12.9225072 C18.317211,12.7514098 18.25,12.5451332 18.25,12.3333917 L18.25,2.6 L18.25,2.6 Z" id="矩形" fill="#D64800" opacity="0.498641241" filter="url(#filter-17)"></path>
                                <path d="M18.25,2.6 L24.1,2.6 L24.1,12.3333917 C24.1,12.8856764 23.6522847,13.3333917 23.1,13.3333917 C22.8882585,13.3333917 22.6819819,13.2661807 22.5108845,13.1414405 L21.175,12.1675015 L21.175,12.1675015 L19.8391155,13.1414405 C19.3928425,13.4668 18.7673107,13.3687803 18.4419512,12.9225072 C18.317211,12.7514098 18.25,12.5451332 18.25,12.3333917 L18.25,2.6 L18.25,2.6 Z" id="矩形" fill="#FFFFFF"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>