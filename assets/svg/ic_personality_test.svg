<?xml version="1.0" encoding="UTF-8"?>
<svg width="76px" height="76px" viewBox="0 0 76 76" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" gradientTransform="translate(0.500000,0.500000),rotate(90.000000),scale(1.000000,1.009929),translate(-0.500000,-0.500000)" id="radialGradient-1">
            <stop stop-color="#BFE4B3" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
        <rect id="path-2" x="0" y="0" width="56" height="56" rx="20"></rect>
        <filter x="-27.7%" y="-25.9%" width="155.4%" height="155.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.879047383   0 0 0 0 0.899073366   0 0 0 0 0.932450004  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="75.1551622%" y2="100%" id="linearGradient-4">
            <stop stop-color="#34CD07" offset="0%"></stop>
            <stop stop-color="#369D10" offset="100%"></stop>
        </linearGradient>
        <path d="M8.1288226,0.0145898119 L24.8000264,0.0751770565 C27.008448,0.084986535 28.7925042,1.88125647 28.7872462,4.08969361 L28.722568,24.3748032 C28.7156455,26.5459228 26.9780419,28.3151162 24.8073994,28.3611502 L8.19909676,28.7133713 C5.99045438,28.7602111 4.16202478,27.0077239 4.11518493,24.7990815 C4.11458547,24.7708151 4.11428571,24.7425432 4.11428571,24.7142705 L4.11428571,4.0145634 C4.11428571,1.8054244 5.90514671,0.0145633967 8.11428571,0.0145633967 C8.11913135,0.0145633967 8.12397699,0.0145722018 8.1288226,0.0145898119 Z" id="path-5"></path>
        <filter x="-2.0%" y="-1.7%" width="104.1%" height="103.5%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.31372549   0 0 0 0 0.88627451   0 0 0 0 0.392156863  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M4,0 L21.3714286,0 C23.5805676,-4.05812251e-16 25.3714286,1.790861 25.3714286,4 L25.3714286,24.1142857 C25.3714286,26.3234247 23.5805676,28.1142857 21.3714286,28.1142857 L4,28.1142857 C1.790861,28.1142857 2.705415e-16,26.3234247 0,24.1142857 L0,4 C-2.705415e-16,1.790861 1.790861,4.05812251e-16 4,0 Z" id="path-7"></path>
        <filter x="-15.8%" y="-7.1%" width="131.5%" height="128.5%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.207843137   0 0 0 0 0.8   0 0 0 0 0.0274509804  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.8978716%" id="linearGradient-10">
            <stop stop-color="#36CC0A" offset="0%"></stop>
            <stop stop-color="#4DB525" offset="100%"></stop>
        </linearGradient>
        <filter x="-28.5%" y="-21.2%" width="157.0%" height="142.5%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.8978716%" id="linearGradient-12">
            <stop stop-color="#36CC0A" offset="0%"></stop>
            <stop stop-color="#4DB525" offset="100%"></stop>
        </linearGradient>
        <filter x="-26.9%" y="-21.2%" width="153.9%" height="142.5%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M4,2.74285714 L20.6857143,2.74285714 C22.8948533,2.74285714 24.6857143,4.53371814 24.6857143,6.74285714 L24.6857143,28.2285714 C24.6857143,30.4377104 22.8948533,32.2285714 20.6857143,32.2285714 L4,32.2285714 C1.790861,32.2285714 2.705415e-16,30.4377104 0,28.2285714 L0,6.74285714 C-7.1463071e-16,4.53371814 1.790861,2.74285714 4,2.74285714 Z" id="path-14"></path>
        <filter x="-8.1%" y="-3.4%" width="116.2%" height="113.6%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.415686275   0 0 0 0 0.733333333   0 0 0 0 0.298039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M4,2.05714286 L20.6857143,2.05714286 C22.8948533,2.05714286 24.6857143,3.84800386 24.6857143,6.05714286 L24.6857143,28.2285714 C24.6857143,30.4377104 22.8948533,32.2285714 20.6857143,32.2285714 L4,32.2285714 C1.790861,32.2285714 2.705415e-16,30.4377104 0,28.2285714 L0,6.05714286 C-7.1463071e-16,3.84800386 1.790861,2.05714286 4,2.05714286 Z" id="path-16"></path>
        <filter x="-2.0%" y="-1.7%" width="104.1%" height="103.3%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.226386159   0 0 0 0 0.69092604   0 0 0 0 0.0757245761  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-75.0%" y="-75.0%" width="250.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-18">
            <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <text id="text-19" font-family="AlibabaPuHuiTiH, Alibaba PuHuiTi" font-size="17" font-weight="normal" line-spacing="17">
            <tspan x="4" y="26">测</tspan>
        </text>
        <filter x="-5.9%" y="-5.9%" width="111.8%" height="117.6%" filterUnits="objectBoundingBox" id="filter-20">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.176416922   0 0 0 0 0.609547822   0 0 0 0 0.0416650869  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.51188777 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-5.9%" y="-5.9%" width="111.8%" height="117.6%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.176416922   0 0 0 0 0.609547822   0 0 0 0 0.0416650869  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.51188777 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-196.000000, -224.000000)">
            <g id="编组-17" transform="translate(8.000000, 224.000000)">
                <g id="编组-8" transform="translate(188.000000, 0.000000)">
                    <g id="编组-7" transform="translate(10.000000, 10.000000)">
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <rect stroke="#FFFFFF" stroke-width="1" stroke-linejoin="square" fill="url(#radialGradient-1)" fill-rule="evenodd" x="0.5" y="0.5" width="55" height="55" rx="20"></rect>
                        </g>
                        <g id="添加" transform="translate(4.000000, 4.000000)">
                            <rect id="矩形" x="0" y="0" width="48" height="48"></rect>
                            <g id="编组-9" transform="translate(9.600000, 8.228571)">
                                <g id="矩形">
                                    <use fill="url(#linearGradient-4)" fill-rule="evenodd" xlink:href="#path-5"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                                </g>
                                <g id="矩形-+-矩形-蒙版" transform="translate(2.057143, 2.057143)">
                                    <mask id="mask-8" fill="white">
                                        <use xlink:href="#path-7"></use>
                                    </mask>
                                    <g id="蒙版" opacity="0.317075312" fill="black" fill-opacity="1">
                                        <use filter="url(#filter-9)" xlink:href="#path-7"></use>
                                    </g>
                                    <path d="M2.11986819,-1.62914128 L22.8621733,26.6081407 L5.86334944,26.6132469 C3.65421054,26.6139105 1.86281167,24.8235875 1.86214808,22.6144486 C1.8621437,22.5998615 1.86221911,22.5852745 1.86237431,22.5706882 L2.11986819,-1.62914128 L2.11986819,-1.62914128 Z" id="矩形" fill="url(#linearGradient-10)" filter="url(#filter-11)" mask="url(#mask-8)"></path>
                                    <path d="M1.16074704,-2.00816327 L23.438099,26.2354397 L5.16074704,26.2354397 C2.95160804,26.2354397 1.16074704,24.4445787 1.16074704,22.2354397 L1.16074704,-2.00816327 L1.16074704,-2.00816327 Z" id="矩形" fill="url(#linearGradient-12)" filter="url(#filter-13)" mask="url(#mask-8)"></path>
                                </g>
                                <g id="矩形" opacity="0.600676037">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-14"></use>
                                </g>
                                <g id="矩形" opacity="0.399044219" fill="black" fill-opacity="1">
                                    <use filter="url(#filter-17)" xlink:href="#path-16"></use>
                                </g>
                                <circle id="椭圆形" fill="#41B411" opacity="0.775638399" filter="url(#filter-18)" cx="12.6857143" cy="17.8" r="8"></circle>
                                <g id="测" fill-rule="nonzero" fill="#FFFFFF">
                                    <use xlink:href="#text-19"></use>
                                    <use fill-opacity="1" filter="url(#filter-20)" xlink:href="#text-19"></use>
                                    <use fill-opacity="1" filter="url(#filter-21)" xlink:href="#text-19"></use>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>