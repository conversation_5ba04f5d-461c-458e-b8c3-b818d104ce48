<?xml version="1.0" encoding="UTF-8"?>
<svg width="76px" height="76px" viewBox="0 0 76 76" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" gradientTransform="translate(0.500000,0.500000),rotate(90.000000),scale(1.000000,1.009929),translate(-0.500000,-0.500000)" id="radialGradient-1">
            <stop stop-color="#A6C3FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
        <rect id="path-2" x="0" y="0" width="56" height="56" rx="20"></rect>
        <filter x="-27.7%" y="-25.9%" width="155.4%" height="155.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.879047383   0 0 0 0 0.899073366   0 0 0 0 0.932450004  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-107.1%" y="-107.1%" width="314.3%" height="314.3%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M6,0 L25.2588085,0 C26.7727268,1.90710703e-16 28,1.22727322 28,2.74119146 L28,2.74119146 L28,2.74119146 L28,26.3988561 C28,29.7125646 25.3137085,32.3988561 22,32.3988561 L3,32.3988561 C1.34314575,32.3988561 2.02906125e-16,31.0557103 0,29.3988561 L0,6 C-4.05812251e-16,2.6862915 2.6862915,-2.05581688e-15 6,0 Z" id="path-5"></path>
        <filter x="-34.9%" y="-47.2%" width="169.8%" height="194.4%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-34.9%" y="-47.2%" width="169.8%" height="194.4%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M6,0 L25.1585254,0 C26.7278285,-1.9415261e-15 28,1.27217152 28,2.84147462 L28,2.84147462 L28,2.84147462 L28,23.2204057 C28,26.5341142 25.3137085,29.2204057 22,29.2204057 L3,29.2204057 C1.34314575,29.2204057 2.02906125e-16,27.87726 0,26.2204057 L0,6 C-4.05812251e-16,2.6862915 2.6862915,6.08718376e-16 6,0 Z" id="path-9"></path>
        <filter x="-5.4%" y="-5.1%" width="110.7%" height="110.3%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.176470588   0 0 0 0 0.443137255   0 0 0 0 0.988235294  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="14.823043%" y2="99.4077456%" id="linearGradient-11">
            <stop stop-color="#4EB5FF" offset="0%"></stop>
            <stop stop-color="#0070FF" offset="100%"></stop>
        </linearGradient>
        <path d="M6,0 L26,0 C27.1045695,1.75920346e-15 28,0.8954305 28,2 L28,2.84147462 L28,2.84147462 L28,23.2204057 C28,26.5341142 25.3137085,29.2204057 22,29.2204057 L2,29.2204057 C0.8954305,29.2204057 1.3527075e-16,28.3249752 0,27.2204057 L0,6 C-1.29399067e-15,2.6862915 2.6862915,-2.79460044e-16 6,0 Z" id="path-12"></path>
        <filter x="-1.8%" y="-1.7%" width="103.6%" height="103.4%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.224763005   0 0 0 0 0.476410616   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.330656892 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-101.0%" y="-141.7%" width="302.0%" height="383.3%" filterUnits="objectBoundingBox" id="filter-15">
            <feGaussianBlur stdDeviation="6" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <rect id="path-16" x="7" y="9.71591992" width="14" height="3.17635844" rx="1.58817922"></rect>
        <filter x="-25.0%" y="-78.7%" width="150.0%" height="320.4%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0745098039   0 0 0 0 0.309803922   0 0 0 0 0.815686275  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-21.4%" y="-63.0%" width="142.9%" height="288.9%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.251647703   0 0 0 0 0.781633018  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-19" x="7" y="17.3391802" width="14" height="3.17635844" rx="1.58817922"></rect>
        <filter x="-25.0%" y="-78.7%" width="150.0%" height="320.4%" filterUnits="objectBoundingBox" id="filter-20">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0745098039   0 0 0 0 0.309803922   0 0 0 0 0.815686275  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-21.4%" y="-63.0%" width="142.9%" height="288.9%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.251647703   0 0 0 0 0.781633018  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M14.3181818,0 C16.2511784,-3.55085719e-16 17.8181818,1.56700338 17.8181818,3.5 L17.8172727,3.811 L19.9090909,3.81163012 C21.5659452,3.81163012 22.9090909,5.15477587 22.9090909,6.81163012 L22.9090909,8.28541468 C22.9090909,9.94226893 21.5659452,11.2854147 19.9090909,11.2854147 L8.72727273,11.2854147 C7.07041848,11.2854147 5.72727273,9.94226893 5.72727273,8.28541468 L5.72727273,6.81163012 C5.72727273,5.15477587 7.07041848,3.81163012 8.72727273,3.81163012 L10.8172727,3.811 L10.8181818,3.5 C10.8181818,1.56700338 12.3851852,3.55085719e-16 14.3181818,0 Z" id="path-22"></path>
        <filter x="-20.4%" y="-22.2%" width="140.7%" height="162.0%" filterUnits="objectBoundingBox" id="filter-23">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.253203229   0 0 0 0 0.797590173  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-17.5%" y="-17.7%" width="134.9%" height="153.2%" filterUnits="objectBoundingBox" id="filter-24">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.180392157   0 0 0 0 0.439215686   0 0 0 0 0.984313725  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <circle id="path-25" cx="14.5" cy="4.5" r="1.5"></circle>
        <filter x="-116.7%" y="-83.3%" width="333.3%" height="333.3%" filterUnits="objectBoundingBox" id="filter-26">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0745098039   0 0 0 0 0.309803922   0 0 0 0 0.815686275  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-100.0%" y="-66.7%" width="300.0%" height="300.0%" filterUnits="objectBoundingBox" id="filter-27">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.180392157   0 0 0 0 0.439215686   0 0 0 0 0.984313725  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-8.000000, -224.000000)">
            <g id="编组-17" transform="translate(8.000000, 224.000000)">
                <g id="编组-7" transform="translate(10.000000, 10.000000)">
                    <g id="矩形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <rect stroke="#FFFFFF" stroke-width="1" stroke-linejoin="square" fill="url(#radialGradient-1)" fill-rule="evenodd" x="0.5" y="0.5" width="55" height="55" rx="20"></rect>
                    </g>
                    <g id="资料" transform="translate(14.000000, 8.800000)">
                        <rect id="矩形" filter="url(#filter-4)" x="0" y="5.2" width="28" height="28"></rect>
                        <g id="编组-13">
                            <g id="编组-11" transform="translate(0.000000, 6.801144)">
                                <g id="形状结合">
                                    <mask id="mask-6" fill="white">
                                        <use xlink:href="#path-5"></use>
                                    </mask>
                                    <g id="蒙版" opacity="0.278834025"></g>
                                    <path d="M14.2094862,-6.92326025 C15.8663404,-6.92326025 17.2094862,-5.5801145 17.2094862,-3.92326025 L17.2088814,-1.69226025 L18.6916996,-1.69161106 C20.3485539,-1.69161106 21.6916996,-0.348465308 21.6916996,1.30838894 L21.6916996,2.7821735 C21.6916996,4.43902775 20.3485539,5.7821735 18.6916996,5.7821735 L7.50988142,5.7821735 C5.85302717,5.7821735 4.50988142,4.43902775 4.50988142,2.7821735 L4.50988142,1.30838894 C4.50988142,-0.348465308 5.85302717,-1.69161106 7.50988142,-1.69161106 L8.99188142,-1.69226025 L8.99209486,-3.92326025 C8.99209486,-5.5801145 10.3352406,-6.92326025 11.9920949,-6.92326025 L14.2094862,-6.92326025 Z M13.0644269,-5.65271687 C12.0100651,-5.65271687 11.155336,-4.79945441 11.155336,-3.74690181 C11.155336,-2.69434922 12.0100651,-1.84108675 13.0644269,-1.84108675 C14.1187887,-1.84108675 14.9735178,-2.69434922 14.9735178,-3.74690181 C14.9735178,-4.79945441 14.1187887,-5.65271687 13.0644269,-5.65271687 Z" fill="#FF0097" filter="url(#filter-7)" mask="url(#mask-6)"></path>
                                    <path d="M16.0355731,-7.55853194 C17.6924274,-7.55853194 19.0355731,-6.21538618 19.0355731,-4.55853194 L19.0349684,-2.32753194 L20.5177866,-2.32688275 C22.1746408,-2.32688275 23.5177866,-0.983736995 23.5177866,0.673117255 L23.5177866,2.14690181 C23.5177866,3.80375606 22.1746408,5.14690181 20.5177866,5.14690181 L9.33596838,5.14690181 C7.67911413,5.14690181 6.33596838,3.80375606 6.33596838,2.14690181 L6.33596838,0.673117255 C6.33596838,-0.983736995 7.67911413,-2.32688275 9.33596838,-2.32688275 L10.8179684,-2.32753194 L10.8181818,-4.55853194 C10.8181818,-6.21538618 12.1613276,-7.55853194 13.8181818,-7.55853194 L16.0355731,-7.55853194 Z M14.8905138,-6.28798856 C13.836152,-6.28798856 12.9814229,-5.43472609 12.9814229,-4.3821735 C12.9814229,-3.3296209 13.836152,-2.47635844 14.8905138,-2.47635844 C15.9448756,-2.47635844 16.7996047,-3.3296209 16.7996047,-4.3821735 C16.7996047,-5.43472609 15.9448756,-6.28798856 14.8905138,-6.28798856 Z" fill="#FF0097" filter="url(#filter-8)" mask="url(#mask-6)"></path>
                                </g>
                                <g id="矩形" opacity="0.503256662">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-9"></use>
                                </g>
                                <g id="路径-8" transform="translate(0.000000, 0.179594)">
                                    <mask id="mask-13" fill="white">
                                        <use xlink:href="#path-12"></use>
                                    </mask>
                                    <g id="蒙版">
                                        <use fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-12"></use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-12"></use>
                                    </g>
                                    <polygon fill="#FFFFFF" opacity="0.216006324" mask="url(#mask-13)" points="28 1.23041526 0 29.2204057 0 1.44783321 0 -0.750137647 28.5353357 -0.980738219"></polygon>
                                </g>
                                <rect id="矩形" fill-opacity="0.3" fill="#E02E97" filter="url(#filter-15)" x="5.09090909" y="8.89380362" width="17.8181818" height="12.7054337" rx="3"></rect>
                                <g id="矩形" opacity="0.762052264">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-16"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-16"></use>
                                </g>
                                <g id="矩形" opacity="0.762052264">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-20)" xlink:href="#path-19"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-19"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-19"></use>
                                </g>
                            </g>
                            <g id="形状结合" opacity="0.601539975">
                                <use fill="black" fill-opacity="1" filter="url(#filter-23)" xlink:href="#path-22"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-22"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-24)" xlink:href="#path-22"></use>
                            </g>
                            <g id="椭圆形" opacity="0.601539975">
                                <use fill="black" fill-opacity="1" filter="url(#filter-26)" xlink:href="#path-25"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-25"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-27)" xlink:href="#path-25"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>