<?xml version="1.0" encoding="UTF-8"?>
<svg width="76px" height="76px" viewBox="0 0 76 76" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7</title>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" gradientTransform="translate(0.500000,0.500000),rotate(90.000000),scale(1.000000,1.009929),translate(-0.500000,-0.500000)" id="radialGradient-1">
            <stop stop-color="#FFBFE5" stop-opacity="0.0938592657" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
        <rect id="path-2" x="0" y="0" width="56" height="56" rx="20"></rect>
        <filter x="-27.7%" y="-25.9%" width="155.4%" height="155.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.879047383   0 0 0 0 0.899073366   0 0 0 0 0.932450004  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M12.5694013,18 L17.5972654,18 C18.3548095,18 19.0473357,18.4280048 19.3861197,19.1055728 L23.3861197,27.1055728 C23.8800982,28.0935298 23.4796495,29.2948759 22.4916925,29.7888544 C22.2139817,29.9277098 21.9077555,30 21.5972654,30 L8.56940131,30 C7.46483181,30 6.56940131,29.1045695 6.56940131,28 C6.56940131,27.6895098 6.64169149,27.3832837 6.78054693,27.1055728 L10.7805469,19.1055728 C11.119331,18.4280048 11.8118572,18 12.5694013,18 Z" id="path-4"></path>
        <filter x="-35.9%" y="-41.7%" width="171.8%" height="216.7%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.839640742   0 0 0 0 0.285560273   0 0 0 0 0.610277199  0 0 0 0.401855469 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-23.1%" y="-20.8%" width="146.2%" height="175.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.850048563   0 0 0 0 0   0 0 0 0 0.504195471  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="1" dy="-1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M16.5991455,17.3333333 C17.4401584,17.3333333 18.191322,17.8594688 18.4787323,18.6498472 L19.4543333,21.3326296 L9.87933333,21.3326296 L10.854601,18.6498472 C11.1420113,17.8594688 11.893175,17.3333333 12.7341879,17.3333333 L16.5991455,17.3333333 Z" id="path-7"></path>
        <filter x="-5.1%" y="-12.3%" width="110.3%" height="124.6%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.850048563   0 0 0 0 0   0 0 0 0 0.504195471  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#F2269D" offset="0%"></stop>
            <stop stop-color="#E52152" offset="100%"></stop>
        </linearGradient>
        <filter x="-47.2%" y="-112.5%" width="194.5%" height="325.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M4,0 L26,0 C28.209139,-4.05812251e-16 30,1.790861 30,4 L30,18.3811035 C30,20.5902425 28.209139,22.3811035 26,22.3811035 L4,22.3811035 C1.790861,22.3811035 2.705415e-16,20.5902425 0,18.3811035 L0,4 C-2.705415e-16,1.790861 1.790861,4.05812251e-16 4,0 Z" id="path-11"></path>
        <filter x="-23.3%" y="-22.3%" width="146.7%" height="162.6%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.945098039   0 0 0 0 0.156862745   0 0 0 0 0.580392157  0 0 0 0.601726719 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M4,0 L19.0243902,0 L19.0243902,0 L27,0 C28.6568542,1.167223e-15 30,1.34314575 30,3 L30,18.4135185 C30,20.6164534 28.2188106,22.4047341 26.0158931,22.4134869 L4.01589308,22.5008995 C1.80677152,22.509677 0.00880907856,20.7259457 3.15738662e-05,18.5168242 C1.05246429e-05,18.5115265 -1.33632416e-15,18.5062288 0,18.5009311 L0,4 C-2.705415e-16,1.790861 1.790861,4.05812251e-16 4,0 Z" id="path-13"></path>
        <filter x="-1.7%" y="-2.2%" width="103.3%" height="104.4%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.952941176   0 0 0 0 0.57254902   0 0 0 0 0.823529412  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-120.0%" y="-124.1%" width="340.0%" height="348.3%" filterUnits="objectBoundingBox" id="filter-15">
            <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-85.1%" y="-46.0%" width="260.8%" height="186.8%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.945098039   0 0 0 0 0.145098039   0 0 0 0 0.623529412  0 0 0 0.50030731 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="37.6348108%" y1="0%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#F43EAA" offset="0%"></stop>
            <stop stop-color="#FF70C5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="15.7727363%" y1="75.0593658%" x2="80.2874429%" y2="50%" id="linearGradient-18">
            <stop stop-color="#D90181" offset="0%"></stop>
            <stop stop-color="#F2259E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="16.4785389%" y1="74.6312081%" x2="83.6781774%" y2="33.8662042%" id="linearGradient-19">
            <stop stop-color="#FF2CAA" offset="0%"></stop>
            <stop stop-color="#F2259F" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-290.000000, -224.000000)">
            <g id="编组-17" transform="translate(8.000000, 224.000000)">
                <g id="编组-8" transform="translate(282.000000, 0.000000)">
                    <g id="编组-7" transform="translate(10.000000, 10.000000)">
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <rect stroke="#FFFFFF" stroke-width="1" stroke-linejoin="square" fill="url(#radialGradient-1)" fill-rule="evenodd" x="0.5" y="0.5" width="55" height="55" rx="20"></rect>
                        </g>
                        <g id="编组-9" transform="translate(13.000000, 13.000000)">
                            <g id="矩形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                                <use fill="#F2269F" fill-rule="evenodd" xlink:href="#path-4"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-4"></use>
                            </g>
                            <g id="形状结合">
                                <use fill="#F2269F" fill-rule="evenodd" xlink:href="#path-7"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                            </g>
                            <path d="M18.9543033,16.6666667 C19.7971916,16.6666667 20.5496085,17.1951158 20.8356324,17.9879909 L22.2822565,21.9996667 L9.71725647,21.9996667 L11.1643676,17.9879909 C11.4503915,17.1951158 12.2028084,16.6666667 13.0456967,16.6666667 L18.9543033,16.6666667 Z" id="形状结合" fill="url(#linearGradient-9)" filter="url(#filter-10)"></path>
                            <g id="矩形" opacity="0.61949521">
                                <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-11"></use>
                            </g>
                            <g id="矩形" opacity="0.352405366">
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-13"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                            </g>
                            <ellipse id="椭圆形" fill="#F1269D" opacity="0.394348145" filter="url(#filter-15)" cx="22.6666667" cy="9.5" rx="5" ry="4.83333333"></ellipse>
                            <g id="编组-43" filter="url(#filter-16)" transform="translate(15.789712, 9.712788) rotate(-90.000000) translate(-15.789712, -9.712788) translate(10.502500, -0.076925)">
                                <path d="M0.343460805,0 C-0.275449041,3.15189383 -0.0579110485,5.60611945 0.996074782,7.36267686 C2.64326294,10.1078566 7.43638088,11.1776757 6.98591503,14.1075617" id="路径-2" stroke="url(#linearGradient-17)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                <ellipse id="椭圆形" fill="url(#linearGradient-18)" cx="7.53876257" cy="16.5794245" rx="3.03566239" ry="3"></ellipse>
                                <ellipse id="椭圆形" fill="url(#linearGradient-19)" cx="7.39061121" cy="15.7196164" rx="2.88751103" ry="2.8535891"></ellipse>
                            </g>
                            <rect id="矩形" fill="#FFFFFF" x="12" y="18" width="5.33333333" height="1.33333333" rx="0.666666667"></rect>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>