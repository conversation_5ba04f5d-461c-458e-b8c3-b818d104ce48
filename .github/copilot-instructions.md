# This is a flutter project.

## project architecture

- Add a new folder structure to the project.
- The folder structure should implement Clean Architecture patterns.
- The folder structure should be under the lib / src folder.
- This project uses GetX as state management.
- The folder structure should be like this:
- only search for the folder structure under the lib / src folder.

```
lib/
│   ├── src/                                  # 源代码
│   │   ├── app/                              # 应用级代码
│   │   │   ├── bindings/                     # 应用级绑定
│   │   │   ├── config/                       # 应用配置 (主题, 本地化等)
│   │   │   ├── routes/                       # 应用路由
│   │   │   └── di.dart                       # 依赖注入
│   │   ├── core/                             # 核心代码
│   │   │   ├── errors/                       # 错误处理
│   │   │   ├── network/                      # 网络请求
│   │   │   ├── usecases/                     # 通用 UseCase 接口
│   │   │   ├── utils/                        # 通用工具函数
│   │   │   └── constants/                    # 应用常量
│   │   ├── features/                         # 特性模块
│   │   │   └── feature_example/              # 单个特性模块
│   │   │       ├── data/                     # 数据层
│   │   │       │   ├── datasources/          # 数据源 (本地/远程)
│   │   │       │   ├── models/               # 数据模型
│   │   │       │   └── repositories/         # 仓库实现
│   │   │       ├── domain/                   # 领域层
│   │   │       │   ├── entities/             # 业务实体
│   │   │       │   ├── repositories/         # 仓库接口
│   │   │       │   └── usecases/             # 用例
│   │   │       └── presentation/             # 表现层
│   │   │           ├── bindings/             # 特性绑定
│   │   │           ├── controllers/          # 控制器
│   │   │           ├── pages/                # 页面/屏幕
│   │   │           └── widgets/              # 特性相关的自定义小部件
│   │   └── main.dart                         # 应用入口
│   │   └── injection_container.dart          # 依赖注入管理                       # 依赖注入测试
│   ├── generated_plugin_registrant.dart      # 插件注册
│   ├── main.dart                             # 应用入口
│   ├── injection_container.dart              # 依赖注入管理
│   ├── l10n/                                 # 本地化文件
│   │   ├── arb/                             # 本地化文件
│   │   │   ├── intl_en.arb                  # 英文
│   │   │   ├── intl_zh.arb                  # 中文
│   │   │   └── ...
│   │   └── intl.dart                        # 本地化生成文件
│   ├── assets/                              # 资源文件
│   │   ├── images/                          # 图片资源
│   │   ├── fonts/                           # 字体资源
│   │   ├── json/                            # JSON 资源
│   │   ├── icons/                           # 图标资源
│   │   └── ...
│   ├── themes/                              # 主题文件
│       └── app_theme.dart                  # 应用主题
│   ├── localization/                        # 本地化文件
│       ├── app_localizations.dart          # 应用本地化
│       ├── app_localizations_en.dart       # 英文本地化
│       ├── app_localizations_zh.dart       # 中文本地化
│       └── ...
├── test_driver/                            # 测试驱动
├── integration_test/                       # 集成测试                             # VSCode 配置
├── pubspec.yaml
└── ...
```

## utils

- use shared_preferences to save data to local storage
- create a storage class to manage local storage

## authentication

- wechat login,apple login,phone login with verification code
- save user info to local storage
- save user token to local storage
- use shared_preferences to save user info and token
- use dio to send network request
- use getx to manage state

## splash screen

- check if user is accepted the agreement
- if user is accepted the agreement, go to home page
- if user is not accepted the agreement, show agreement dialog
